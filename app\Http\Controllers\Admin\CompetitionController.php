<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class CompetitionController extends Controller
{
    /**
     * Display a listing of competitions.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // List all competitions
        return view('admin.competitions.index');
    }

    /**
     * Show the form for creating a new competition.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        // Show form to create a new competition
        return view('admin.competitions.create');
    }

    /**
     * Store a newly created competition.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Validate and store the new competition
        
        return redirect()->route('admin.competitions.index')
            ->with('success', 'Competition created successfully!');
    }

    /**
     * Display the specified competition.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        // Show competition details
        return view('admin.competitions.show');
    }

    /**
     * Show the form for editing the specified competition.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        // Show form to edit competition
        return view('admin.competitions.edit');
    }

    /**
     * Update the specified competition.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        // Validate and update the competition
        
        return redirect()->route('admin.competitions.index')
            ->with('success', 'Competition updated successfully!');
    }

    /**
     * Remove the specified competition.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        // Delete the competition
        
        return redirect()->route('admin.competitions.index')
            ->with('success', 'Competition deleted successfully!');
    }
}