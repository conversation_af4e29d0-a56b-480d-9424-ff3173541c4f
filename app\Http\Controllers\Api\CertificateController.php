<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Certificate;
use App\Models\User;
use App\Models\CourseLevel;
use App\Models\StudentProgress;
use App\Models\Subscription;
use App\Models\Notification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;

class CertificateController extends Controller
{
    /**
     * Display certificates with filtering
     */
    public function index(Request $request)
    {
        $query = Certificate::with(['student.profile', 'level.course']);

        // Filter by student
        if ($request->has('student_id')) {
            $query->where('student_id', $request->student_id);
        }

        // Filter by level
        if ($request->has('level_id')) {
            $query->where('level_id', $request->level_id);
        }

        // Filter by date range
        if ($request->has('from_date')) {
            $query->whereDate('issued_at', '>=', $request->from_date);
        }
        if ($request->has('to_date')) {
            $query->whereDate('issued_at', '<=', $request->to_date);
        }

        // Search by student name
        if ($request->has('search')) {
            $search = $request->search;
            $query->whereHas('student', function($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('second_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        $certificates = $query->orderBy('issued_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $certificates
        ]);
    }

    /**
     * Issue a new certificate
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'student_id' => 'required|exists:users,id',
            'level_id' => 'required|exists:course_levels,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Validate student role
            $student = User::where('id', $request->student_id)
                ->where('role', 'student')
                ->first();

            if (!$student) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid student ID or user is not a student'
                ], 422);
            }

            // Check if certificate already exists
            $existingCertificate = Certificate::where('student_id', $request->student_id)
                ->where('level_id', $request->level_id)
                ->first();

            if ($existingCertificate) {
                return response()->json([
                    'success' => false,
                    'message' => 'Certificate already issued for this student and level'
                ], 409);
            }

            // Validate that student has completed the level
            $isEligible = $this->checkCertificateEligibility($request->student_id, $request->level_id);

            if (!$isEligible['eligible']) {
                return response()->json([
                    'success' => false,
                    'message' => $isEligible['reason']
                ], 422);
            }

            // Create certificate
            $certificate = Certificate::create([
                'student_id' => $request->student_id,
                'level_id' => $request->level_id,
                'issued_at' => now(),
            ]);

            // Send notification to student
            $this->sendCertificateNotification($certificate);

            return response()->json([
                'success' => true,
                'message' => 'Certificate issued successfully',
                'data' => $certificate->load(['student.profile', 'level.course'])
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Certificate issuance failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display specific certificate
     */
    public function show(Certificate $certificate)
    {
        return response()->json([
            'success' => true,
            'data' => $certificate->load([
                'student.profile',
                'level.course'
            ])
        ]);
    }

    /**
     * Update certificate (limited updates allowed)
     */
    public function update(Request $request, Certificate $certificate)
    {
        $validator = Validator::make($request->all(), [
            'issued_at' => 'sometimes|date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Only allow updating the issued date
            if ($request->has('issued_at')) {
                $certificate->update(['issued_at' => $request->issued_at]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Certificate updated successfully',
                'data' => $certificate->load(['student.profile', 'level.course'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Certificate update failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete certificate
     */
    public function destroy(Certificate $certificate)
    {
        try {
            $certificate->delete();

            return response()->json([
                'success' => true,
                'message' => 'Certificate deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Certificate deletion failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Issue certificate automatically based on completion
     */
    public function issue(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'student_id' => 'required|exists:users,id',
            'level_id' => 'required|exists:course_levels,id',
            'force_issue' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $student = User::where('id', $request->student_id)
                ->where('role', 'student')
                ->first();

            if (!$student) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid student ID or user is not a student'
                ], 422);
            }

            // Check if certificate already exists
            $existingCertificate = Certificate::where('student_id', $request->student_id)
                ->where('level_id', $request->level_id)
                ->first();

            if ($existingCertificate) {
                return response()->json([
                    'success' => false,
                    'message' => 'Certificate already issued',
                    'data' => $existingCertificate->load(['student.profile', 'level.course'])
                ], 409);
            }

            // Check eligibility unless forced
            if (!$request->boolean('force_issue')) {
                $eligibility = $this->checkCertificateEligibility($request->student_id, $request->level_id);

                if (!$eligibility['eligible']) {
                    return response()->json([
                        'success' => false,
                        'message' => $eligibility['reason'],
                        'eligibility_details' => $eligibility
                    ], 422);
                }
            }

            // Issue certificate
            $certificate = Certificate::create([
                'student_id' => $request->student_id,
                'level_id' => $request->level_id,
                'issued_at' => now(),
            ]);

            // Send notification
            $this->sendCertificateNotification($certificate);

            return response()->json([
                'success' => true,
                'message' => 'Certificate issued successfully',
                'data' => $certificate->load(['student.profile', 'level.course'])
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Certificate issuance failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get student's certificates
     */
    public function getStudentCertificates(User $student, Request $request)
    {
        if ($student->role !== 'student') {
            return response()->json([
                'success' => false,
                'message' => 'User is not a student'
            ], 422);
        }

        $query = Certificate::with(['level.course'])
            ->where('student_id', $student->id);

        // Filter by level if provided
        if ($request->has('level_id')) {
            $query->where('level_id', $request->level_id);
        }

        $certificates = $query->orderBy('issued_at', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => [
                'student' => $student->load('profile'),
                'certificates' => $certificates,
                'total_certificates' => $certificates->count()
            ]
        ]);
    }

    /**
     * Get authenticated student's certificates
     */
    public function getMyCertificates(Request $request)
    {
        $user = Auth::user();

        if ($user->role !== 'student') {
            return response()->json([
                'success' => false,
                'message' => 'Only students can view their certificates'
            ], 403);
        }

        $query = Certificate::with(['level.course'])
            ->where('student_id', $user->id);

        // Filter by level if provided
        if ($request->has('level_id')) {
            $query->where('level_id', $request->level_id);
        }

        $certificates = $query->orderBy('issued_at', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => [
                'certificates' => $certificates,
                'total_certificates' => $certificates->count()
            ]
        ]);
    }

    /**
     * Check certificate eligibility for a student
     */
    public function checkEligibility(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'student_id' => 'required|exists:users,id',
            'level_id' => 'required|exists:course_levels,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        $eligibility = $this->checkCertificateEligibility($request->student_id, $request->level_id);

        return response()->json([
            'success' => true,
            'data' => $eligibility
        ]);
    }

    /**
     * Get certificate statistics
     */
    public function getCertificateStats(Request $request)
    {
        $query = Certificate::query();

        // Filter by date range if provided
        if ($request->has('from_date')) {
            $query->whereDate('issued_at', '>=', $request->from_date);
        }
        if ($request->has('to_date')) {
            $query->whereDate('issued_at', '<=', $request->to_date);
        }

        $stats = [
            'total_certificates' => $query->count(),
            'certificates_this_month' => Certificate::whereMonth('issued_at', now()->month)
                ->whereYear('issued_at', now()->year)
                ->count(),
            'certificates_this_year' => Certificate::whereYear('issued_at', now()->year)
                ->count(),
            'certificates_by_level' => Certificate::with('level.course')
                ->selectRaw('level_id, COUNT(*) as count')
                ->groupBy('level_id')
                ->get()
                ->map(function($cert) {
                    return [
                        'level' => $cert->level->title ?? 'Unknown',
                        'course' => $cert->level->course->name ?? 'Unknown',
                        'count' => $cert->count
                    ];
                }),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Check if student is eligible for certificate
     */
    private function checkCertificateEligibility($studentId, $levelId)
    {
        // Check if student has active subscription
        $hasSubscription = Subscription::where('student_id', $studentId)
            ->where('level_id', $levelId)
            ->where('renewal_status', 'active')
            ->exists();

        if (!$hasSubscription) {
            return [
                'eligible' => false,
                'reason' => 'Student does not have an active subscription for this level'
            ];
        }

        // Get level with lessons
        $level = CourseLevel::with('lessons')->find($levelId);
        $totalLessons = $level->lessons->count();

        if ($totalLessons === 0) {
            return [
                'eligible' => false,
                'reason' => 'No lessons found for this level'
            ];
        }

        // Check lesson completion
        $completedLessons = StudentProgress::where('student_id', $studentId)
            ->where('level_id', $levelId)
            ->where('status', 'completed')
            ->whereNotNull('lesson_id')
            ->count();

        $completionPercentage = ($completedLessons / $totalLessons) * 100;

        // Require at least 80% completion for certificate
        $requiredCompletion = 80;

        if ($completionPercentage < $requiredCompletion) {
            return [
                'eligible' => false,
                'reason' => "Insufficient completion. Required: {$requiredCompletion}%, Current: " . round($completionPercentage, 2) . "%",
                'completion_details' => [
                    'total_lessons' => $totalLessons,
                    'completed_lessons' => $completedLessons,
                    'completion_percentage' => round($completionPercentage, 2),
                    'required_percentage' => $requiredCompletion
                ]
            ];
        }

        return [
            'eligible' => true,
            'reason' => 'Student meets all requirements for certificate',
            'completion_details' => [
                'total_lessons' => $totalLessons,
                'completed_lessons' => $completedLessons,
                'completion_percentage' => round($completionPercentage, 2),
                'required_percentage' => $requiredCompletion
            ]
        ];
    }

    /**
     * Send certificate notification to student
     */
    private function sendCertificateNotification($certificate)
    {
        try {
            $student = $certificate->student;
            $level = $certificate->level;
            $course = $level->course;

            // Create in-app notification
            $notification = Notification::create([
                'user_id' => $student->id,
                'title' => 'Certificate Issued!',
                'message' => "Congratulations! You have been awarded a certificate for completing {$course->name} - {$level->title}.",
                'type' => 'general',
            ]);

            // Send email notification
            if ($student->email) {
                Mail::send('emails.certificate', [
                    'student' => $student,
                    'course' => $course,
                    'level' => $level,
                    'certificate' => $certificate
                ], function ($mail) use ($student) {
                    $mail->to($student->email, $student->first_name . ' ' . $student->second_name)
                         ->subject('Certificate Awarded - Soroban-AlBarq');
                });
            }

        } catch (\Exception $e) {
            // Log error but don't fail certificate creation
            \Log::error('Certificate notification failed: ' . $e->getMessage());
        }
    }
}
