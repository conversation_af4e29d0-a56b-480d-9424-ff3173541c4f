<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CourseLevel;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CourseLevelController extends Controller
{
    /**
     * Display course levels (can be filtered by course)
     */
    public function index(Request $request)
    {
        $query = CourseLevel::with(['course', 'lessons', 'exams']);

        // Filter by course if course parameter is provided in route
        if ($request->route('course')) {
            $course = $request->route('course');
            $query->where('course_id', $course->id);
        }

        // Filter by course_id if provided in query
        if ($request->has('course_id')) {
            $query->where('course_id', $request->course_id);
        }

        // Filter by level number
        if ($request->has('level_number')) {
            $query->where('level_number', $request->level_number);
        }

        $levels = $query->orderBy('level_number')->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $levels
        ]);
    }

    /**
     * Store a new course level
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'course_id' => 'required|exists:courses,id',
            'level_number' => 'required|in:one,two,three',
            'title' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Check if level already exists for this course
            $existingLevel = CourseLevel::where('course_id', $request->course_id)
                ->where('level_number', $request->level_number)
                ->first();

            if ($existingLevel) {
                return response()->json([
                    'success' => false,
                    'message' => 'This level already exists for the course'
                ], 409);
            }

            $level = CourseLevel::create([
                'course_id' => $request->course_id,
                'level_number' => $request->level_number,
                'title' => $request->title,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Course level created successfully',
                'data' => $level->load(['course', 'lessons', 'exams'])
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Course level creation failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display a specific course level
     */
    public function show(CourseLevel $courseLevel)
    {
        return response()->json([
            'success' => true,
            'data' => $courseLevel->load([
                'course',
                'lessons.videoAttachment',
                'lessons.pdfAttachment',
                'lessons.quiz',
                'exams',
                'subscriptions.student',
                'groups.teacher'
            ])
        ]);
    }

    /**
     * Update a course level
     */
    public function update(Request $request, CourseLevel $courseLevel)
    {
        $validator = Validator::make($request->all(), [
            'course_id' => 'sometimes|exists:courses,id',
            'level_number' => 'sometimes|in:one,two,three',
            'title' => 'sometimes|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Check for duplicate level if level_number is being changed
            if ($request->has('level_number') && $request->level_number !== $courseLevel->level_number) {
                $courseId = $request->course_id ?? $courseLevel->course_id;
                $existingLevel = CourseLevel::where('course_id', $courseId)
                    ->where('level_number', $request->level_number)
                    ->where('id', '!=', $courseLevel->id)
                    ->first();

                if ($existingLevel) {
                    return response()->json([
                        'success' => false,
                        'message' => 'This level already exists for the course'
                    ], 409);
                }
            }

            $courseLevel->update($request->only(['course_id', 'level_number', 'title']));

            return response()->json([
                'success' => true,
                'message' => 'Course level updated successfully',
                'data' => $courseLevel->load(['course', 'lessons', 'exams'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Course level update failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a course level
     */
    public function destroy(CourseLevel $courseLevel)
    {
        try {
            // Check if level has active subscriptions
            $activeSubscriptions = $courseLevel->subscriptions()
                ->where('renewal_status', 'active')
                ->count();

            if ($activeSubscriptions > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete level with active subscriptions'
                ], 409);
            }

            $courseLevel->delete();

            return response()->json([
                'success' => true,
                'message' => 'Course level deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Course level deletion failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
