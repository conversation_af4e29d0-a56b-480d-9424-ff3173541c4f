<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Lesson;
use App\Models\CourseLevel;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class LessonController extends Controller
{
    /**
     * Display a listing of lessons
     */
    public function index(Request $request)
    {
        $query = Lesson::with(['courseLevel.course', 'videoAttachment', 'pdfAttachment', 'quiz']);

        // Filter by course level
        if ($request->has('course_level_id')) {
            $query->where('course_level_id', $request->course_level_id);
        }

        // Search by title
        if ($request->has('search')) {
            $query->where('title', 'like', '%' . $request->search . '%');
        }

        $lessons = $query->orderBy('sequence')->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $lessons
        ]);
    }

    /**
     * Store a new lesson
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'course_level_id' => 'required|exists:course_levels,id',
            'day_number' => 'required|integer|min:1',
            'sequence' => 'required|integer|min:1',
            'title' => 'required|string|max:255',
            'video_attachment_id' => 'nullable|exists:attachments,id',
            'pdf_attachment_id' => 'nullable|exists:attachments,id',
            'quiz_id' => 'nullable|exists:quizzes,id',
            'youtube_playlist_url' => 'nullable|url',
            'youtube_playlist_id' => 'nullable|string|max:255',
            'requires_payment' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Extract YouTube playlist ID from URL if provided
            $playlistId = $request->youtube_playlist_id;
            if ($request->youtube_playlist_url && !$playlistId) {
                $playlistId = $this->extractPlaylistId($request->youtube_playlist_url);
            }

            $lesson = Lesson::create([
                'course_level_id' => $request->course_level_id,
                'day_number' => $request->day_number,
                'sequence' => $request->sequence,
                'title' => $request->title,
                'video_attachment_id' => $request->video_attachment_id,
                'pdf_attachment_id' => $request->pdf_attachment_id,
                'quiz_id' => $request->quiz_id,
                'youtube_playlist_url' => $request->youtube_playlist_url,
                'youtube_playlist_id' => $playlistId,
                'requires_payment' => $request->requires_payment ?? true,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Lesson created successfully',
                'data' => $lesson->load(['courseLevel.course', 'videoAttachment', 'pdfAttachment', 'quiz'])
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lesson creation failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display a specific lesson with access control
     */
    public function show(Lesson $lesson)
    {
        $user = Auth::user();

        // Load relationships
        $lesson->load(['courseLevel.course', 'videoAttachment', 'pdfAttachment', 'quiz']);

        // Check if user has access to this lesson
        $hasAccess = $this->checkLessonAccess($user, $lesson);

        $response = [
            'success' => true,
            'data' => $lesson,
            'has_access' => $hasAccess
        ];

        // If user doesn't have access and lesson requires payment, hide sensitive content
        if (!$hasAccess && $lesson->requires_payment) {
            $response['data'] = $lesson->only(['id', 'title', 'day_number', 'sequence', 'requires_payment']);
            $response['message'] = 'This lesson requires an active subscription to access';
        }

        return response()->json($response);
    }

    /**
     * Update a lesson
     */
    public function update(Request $request, Lesson $lesson)
    {
        $validator = Validator::make($request->all(), [
            'course_level_id' => 'sometimes|exists:course_levels,id',
            'day_number' => 'sometimes|integer|min:1',
            'sequence' => 'sometimes|integer|min:1',
            'title' => 'sometimes|string|max:255',
            'video_attachment_id' => 'nullable|exists:attachments,id',
            'pdf_attachment_id' => 'nullable|exists:attachments,id',
            'quiz_id' => 'nullable|exists:quizzes,id',
            'youtube_playlist_url' => 'nullable|url',
            'youtube_playlist_id' => 'nullable|string|max:255',
            'requires_payment' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Extract YouTube playlist ID from URL if provided
            if ($request->has('youtube_playlist_url') && $request->youtube_playlist_url) {
                $playlistId = $request->youtube_playlist_id ?? $this->extractPlaylistId($request->youtube_playlist_url);
                $request->merge(['youtube_playlist_id' => $playlistId]);
            }

            $lesson->update($request->only([
                'course_level_id', 'day_number', 'sequence', 'title',
                'video_attachment_id', 'pdf_attachment_id', 'quiz_id',
                'youtube_playlist_url', 'youtube_playlist_id', 'requires_payment'
            ]));

            return response()->json([
                'success' => true,
                'message' => 'Lesson updated successfully',
                'data' => $lesson->load(['courseLevel.course', 'videoAttachment', 'pdfAttachment', 'quiz'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lesson update failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a lesson
     */
    public function destroy(Lesson $lesson)
    {
        try {
            $lesson->delete();

            return response()->json([
                'success' => true,
                'message' => 'Lesson deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Lesson deletion failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get lessons by course level
     */
    public function getByLevel(CourseLevel $level, Request $request)
    {
        $user = Auth::user();

        $lessons = $level->lessons()
            ->with(['videoAttachment', 'pdfAttachment', 'quiz'])
            ->orderBy('sequence')
            ->get();

        // Check access for each lesson
        $lessons->each(function ($lesson) use ($user) {
            $lesson->has_access = $this->checkLessonAccess($user, $lesson);

            // Hide sensitive content if no access
            if (!$lesson->has_access && $lesson->requires_payment) {
                $lesson->makeHidden(['youtube_playlist_url', 'youtube_playlist_id', 'videoAttachment', 'pdfAttachment']);
            }
        });

        return response()->json([
            'success' => true,
            'data' => $lessons
        ]);
    }

    /**
     * Check if user has access to a lesson
     */
    private function checkLessonAccess($user, $lesson)
    {
        if (!$user) {
            return false;
        }

        // Admins and teachers have full access
        if (in_array($user->role, ['superAdmin', 'admin', 'teacher'])) {
            return true;
        }

        // If lesson doesn't require payment, it's free
        if (!$lesson->requires_payment) {
            return true;
        }

        // Check if user has active subscription for this level
        $hasSubscription = Subscription::where('student_id', $user->id)
            ->where('level_id', $lesson->course_level_id)
            ->where('renewal_status', 'active')
            ->where('start_date', '<=', now())
            ->where('end_date', '>=', now())
            ->exists();

        return $hasSubscription;
    }

    /**
     * Extract YouTube playlist ID from URL
     */
    private function extractPlaylistId($url)
    {
        // Handle different YouTube playlist URL formats
        $patterns = [
            '/[?&]list=([a-zA-Z0-9_-]+)/',
            '/playlist\?list=([a-zA-Z0-9_-]+)/',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $url, $matches)) {
                return $matches[1];
            }
        }

        return null;
    }
}
