<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PaymentController extends Controller
{
    public function index(Request $request)
    {
        $query = Payment::with(['student', 'level.course', 'subscription']);

        // Filter by student
        if ($request->has('student_id')) {
            $query->where('student_id', $request->student_id);
        }

        // Filter by confirmation status
        if ($request->has('confirmed')) {
            $query->where('is_confirmed', $request->boolean('confirmed'));
        }

        // Filter by date range
        if ($request->has('from_date')) {
            $query->whereDate('created_at', '>=', $request->from_date);
        }

        if ($request->has('to_date')) {
            $query->whereDate('created_at', '<=', $request->to_date);
        }

        $payments = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $payments
        ]);
    }

    public function show(Payment $payment)
    {
        return response()->json([
            'success' => true,
            'data' => $payment->load(['student.profile', 'level.course', 'subscription'])
        ]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'student_id' => 'required|exists:users,id',
            'level_id' => 'required|exists:course_levels,id',
            'subscription_id' => 'required|exists:subscriptions,id',
            'amount' => 'required|numeric|min:0',
            'transaction_code' => 'nullable|string|max:255',
            'transaction_pic' => 'nullable|string', // Base64 or URL
            'subscription_start_date' => 'nullable|date',
            'subscription_end_date' => 'nullable|date|after:subscription_start_date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $payment = Payment::create($request->only([
                'student_id', 'level_id', 'subscription_id', 'amount',
                'transaction_code', 'transaction_pic', 
                'subscription_start_date', 'subscription_end_date'
            ]));

            return response()->json([
                'success' => true,
                'message' => 'Payment created successfully',
                'data' => $payment->load(['student', 'level.course', 'subscription'])
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payment creation failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function update(Request $request, Payment $payment)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'sometimes|numeric|min:0',
            'transaction_code' => 'sometimes|string|max:255',
            'transaction_pic' => 'sometimes|string',
            'subscription_start_date' => 'sometimes|date',
            'subscription_end_date' => 'sometimes|date|after:subscription_start_date',
            'is_confirmed' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $payment->update($request->only([
                'amount', 'transaction_code', 'transaction_pic',
                'subscription_start_date', 'subscription_end_date', 'is_confirmed'
            ]));

            return response()->json([
                'success' => true,
                'message' => 'Payment updated successfully',
                'data' => $payment->fresh()->load(['student', 'level.course', 'subscription'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payment update failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Payment $payment)
    {
        try {
            $payment->delete();

            return response()->json([
                'success' => true,
                'message' => 'Payment deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payment deletion failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function confirm(Request $request, Payment $payment)
    {
        try {
            $payment->update(['is_confirmed' => true]);

            // Update subscription status if needed
            if ($payment->subscription) {
                $payment->subscription->update(['renewal_status' => 'active']);
            }

            return response()->json([
                'success' => true,
                'message' => 'Payment confirmed successfully',
                'data' => $payment->fresh()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Payment confirmation failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getStudentPayments(User $student)
    {
        $payments = $student->payments()
            ->with(['level.course', 'subscription'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $payments
        ]);
    }

    public function paymentReports(Request $request)
    {
        $stats = [
            'total_payments' => Payment::count(),
            'confirmed_payments' => Payment::where('is_confirmed', true)->count(),
            'pending_payments' => Payment::where('is_confirmed', false)->count(),
            'total_revenue' => Payment::where('is_confirmed', true)->sum('amount'),
            'revenue_this_month' => Payment::where('is_confirmed', true)
                ->whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('amount'),
            'revenue_today' => Payment::where('is_confirmed', true)
                ->whereDate('created_at', now()->toDateString())
                ->sum('amount'),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
