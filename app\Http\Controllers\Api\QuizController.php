<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Quiz;
use App\Models\Lesson;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class QuizController extends Controller
{
    /**
     * Display a listing of quizzes
     */
    public function index(Request $request)
    {
        $query = Quiz::with(['lesson.courseLevel.course']);

        // Filter by lesson
        if ($request->has('lesson_id')) {
            $query->where('lesson_id', $request->lesson_id);
        }

        // Search by title
        if ($request->has('search')) {
            $query->where('title', 'like', '%' . $request->search . '%');
        }

        $quizzes = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $quizzes
        ]);
    }

    /**
     * Store a new quiz with Google Forms integration
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'lesson_id' => 'required|exists:lessons,id',
            'title' => 'required|string|max:255',
            'iframe_url' => 'required|url',
            'time_limit' => 'nullable|integer|min:1',
            'max_attempts' => 'nullable|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Validate Google Forms URL
            if (!$this->isValidGoogleFormsUrl($request->iframe_url)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid Google Forms URL. Please provide a valid Google Forms embed URL.'
                ], 422);
            }

            $quiz = Quiz::create([
                'lesson_id' => $request->lesson_id,
                'title' => $request->title,
                'iframe_url' => $request->iframe_url,
                'time_limit' => $request->time_limit,
                'max_attempts' => $request->max_attempts ?? 1,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Quiz created successfully',
                'data' => $quiz->load(['lesson.courseLevel.course'])
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Quiz creation failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display a specific quiz
     */
    public function show(Quiz $quiz)
    {
        return response()->json([
            'success' => true,
            'data' => $quiz->load(['lesson.courseLevel.course'])
        ]);
    }

    /**
     * Update a quiz
     */
    public function update(Request $request, Quiz $quiz)
    {
        $validator = Validator::make($request->all(), [
            'lesson_id' => 'sometimes|exists:lessons,id',
            'title' => 'sometimes|string|max:255',
            'iframe_url' => 'sometimes|url',
            'time_limit' => 'nullable|integer|min:1',
            'max_attempts' => 'nullable|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Validate Google Forms URL if provided
            if ($request->has('iframe_url') && !$this->isValidGoogleFormsUrl($request->iframe_url)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid Google Forms URL. Please provide a valid Google Forms embed URL.'
                ], 422);
            }

            $quiz->update($request->only([
                'lesson_id', 'title', 'iframe_url', 'time_limit', 'max_attempts'
            ]));

            return response()->json([
                'success' => true,
                'message' => 'Quiz updated successfully',
                'data' => $quiz->load(['lesson.courseLevel.course'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Quiz update failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a quiz
     */
    public function destroy(Quiz $quiz)
    {
        try {
            $quiz->delete();

            return response()->json([
                'success' => true,
                'message' => 'Quiz deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Quiz deletion failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get quiz by lesson
     */
    public function getByLesson(Lesson $lesson)
    {
        $quiz = $lesson->quiz;

        if (!$quiz) {
            return response()->json([
                'success' => false,
                'message' => 'No quiz found for this lesson'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $quiz
        ]);
    }

    /**
     * Generate Google Forms embed HTML
     */
    public function getQuizEmbed(Quiz $quiz, Request $request)
    {
        $embedUrl = $this->generateGoogleFormsEmbedUrl($quiz->iframe_url);

        $width = $request->get('width', 640);
        $height = $request->get('height', 800);

        $iframeHtml = sprintf(
            '<iframe src="%s" width="%d" height="%d" frameborder="0" marginheight="0" marginwidth="0">Loading…</iframe>',
            $embedUrl,
            $width,
            $height
        );

        return response()->json([
            'success' => true,
            'data' => [
                'quiz_id' => $quiz->id,
                'quiz_title' => $quiz->title,
                'embed_url' => $embedUrl,
                'iframe_html' => $iframeHtml,
                'time_limit' => $quiz->time_limit,
                'max_attempts' => $quiz->max_attempts
            ]
        ]);
    }

    /**
     * Validate Google Forms URL
     */
    private function isValidGoogleFormsUrl($url)
    {
        $patterns = [
            '/^https:\/\/docs\.google\.com\/forms\/d\/[a-zA-Z0-9-_]+\/viewform/',
            '/^https:\/\/forms\.gle\/[a-zA-Z0-9-_]+/',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $url)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Generate Google Forms embed URL
     */
    private function generateGoogleFormsEmbedUrl($originalUrl)
    {
        // Convert regular Google Forms URL to embed URL
        if (strpos($originalUrl, '/viewform') !== false) {
            return str_replace('/viewform', '/viewform?embedded=true', $originalUrl);
        }

        // If it's already an embed URL, return as is
        if (strpos($originalUrl, 'embedded=true') !== false) {
            return $originalUrl;
        }

        // Add embedded parameter
        $separator = strpos($originalUrl, '?') !== false ? '&' : '?';
        return $originalUrl . $separator . 'embedded=true';
    }
}
