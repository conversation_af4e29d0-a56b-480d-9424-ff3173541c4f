<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SubscriptionController extends Controller
{
    public function index(Request $request)
    {
        $query = Subscription::with(['student', 'level.course', 'company']);

        // Filter by student
        if ($request->has('student_id')) {
            $query->where('student_id', $request->student_id);
        }

        // Filter by status
        if ($request->has('status')) {
            $query->where('renewal_status', $request->status);
        }

        // Filter by level
        if ($request->has('level_id')) {
            $query->where('level_id', $request->level_id);
        }

        $subscriptions = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $subscriptions
        ]);
    }

    public function show(Subscription $subscription)
    {
        return response()->json([
            'success' => true,
            'data' => $subscription->load(['student.profile', 'level.course', 'company', 'payments'])
        ]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'student_id' => 'required|exists:users,id',
            'level_id' => 'required|exists:course_levels,id',
            'company_id' => 'nullable|exists:companies,id',
            'amount' => 'required|numeric|min:0',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $subscription = Subscription::create($request->only([
                'student_id', 'level_id', 'company_id', 'amount', 
                'start_date', 'end_date'
            ]));

            return response()->json([
                'success' => true,
                'message' => 'Subscription created successfully',
                'data' => $subscription->load(['student', 'level.course', 'company'])
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Subscription creation failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function update(Request $request, Subscription $subscription)
    {
        $validator = Validator::make($request->all(), [
            'amount' => 'sometimes|numeric|min:0',
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after:start_date',
            'renewal_status' => 'sometimes|in:active,expired,cancelled,pending_renewal',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $subscription->update($request->only([
                'amount', 'start_date', 'end_date', 'renewal_status'
            ]));

            return response()->json([
                'success' => true,
                'message' => 'Subscription updated successfully',
                'data' => $subscription->fresh()->load(['student', 'level.course', 'company'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Subscription update failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy(Subscription $subscription)
    {
        try {
            $subscription->delete();

            return response()->json([
                'success' => true,
                'message' => 'Subscription deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Subscription deletion failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getStudentSubscriptions(User $student)
    {
        $subscriptions = $student->subscriptions()
            ->with(['level.course', 'company'])
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $subscriptions
        ]);
    }

    public function renew(Request $request, Subscription $subscription)
    {
        $validator = Validator::make($request->all(), [
            'end_date' => 'required|date|after:' . $subscription->end_date,
            'amount' => 'sometimes|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation errors',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $subscription->update([
                'end_date' => $request->end_date,
                'amount' => $request->amount ?? $subscription->amount,
                'renewal_status' => 'active'
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Subscription renewed successfully',
                'data' => $subscription->fresh()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Subscription renewal failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getMyCourses(Request $request)
    {
        $student = $request->user();
        
        $subscriptions = $student->subscriptions()
            ->with(['level.course', 'level.lessons'])
            ->where('renewal_status', 'active')
            ->where('end_date', '>=', now())
            ->get();

        return response()->json([
            'success' => true,
            'data' => $subscriptions
        ]);
    }

    public function enrollmentReports(Request $request)
    {
        $stats = [
            'total_subscriptions' => Subscription::count(),
            'active_subscriptions' => Subscription::where('renewal_status', 'active')->count(),
            'expired_subscriptions' => Subscription::where('renewal_status', 'expired')->count(),
            'pending_renewals' => Subscription::where('renewal_status', 'pending_renewal')->count(),
            'revenue_this_month' => Subscription::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->sum('amount'),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
