<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Course;
use App\Models\Enrollment;
use Illuminate\Support\Facades\Auth;

class CourseController extends Controller
{
    /**
     * Display a listing of the courses.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // In a real application, you would fetch courses from the database
        // For now, we'll use dummy data
        $courses = [
            (object)[
                'id' => 1,
                'title' => 'Introduction to Soroban',
                'description' => 'Learn the basics of Soroban calculation',
                'price' => 49.99,
                'image' => 'https://via.placeholder.com/400x200?text=Course+1'
            ],
            (object)[
                'id' => 2,
                'title' => 'Intermediate Soroban',
                'description' => 'Take your Soroban skills to the next level',
                'price' => 69.99,
                'image' => 'https://via.placeholder.com/400x200?text=Course+2'
            ],
            (object)[
                'id' => 3,
                'title' => 'Advanced Soroban Techniques',
                'description' => 'Master complex calculations with Soroban',
                'price' => 89.99,
                'image' => 'https://via.placeholder.com/400x200?text=Course+3'
            ],
        ];

        return view('courses.index', compact('courses'));
    }

    /**
     * Display the specified course.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        // In a real application, you would fetch the course from the database
        // For now, we'll use dummy data
        $course = (object)[
            'id' => $id,
            'title' => 'Course ' . $id,
            'description' => 'This is a detailed description of course ' . $id,
            'price' => 49.99 + ($id * 10),
            'image' => 'https://via.placeholder.com/800x400?text=Course+' . $id,
            'lessons' => [
                (object)['id' => 1, 'title' => 'Lesson 1: Introduction'],
                (object)['id' => 2, 'title' => 'Lesson 2: Basic Techniques'],
                (object)['id' => 3, 'title' => 'Lesson 3: Practice Exercises'],
            ]
        ];

        return view('courses.show', compact('course'));
    }

    /**
     * Display the user's enrolled courses.
     *
     * @return \Illuminate\View\View
     */
    public function myCourses()
    {
        // In a real application, you would fetch the user's enrolled courses from the database
        // For now, we'll use dummy data
        $courses = [
            (object)[
                'id' => 1,
                'title' => 'Introduction to Soroban',
                'description' => 'Learn the basics of Soroban calculation',
                'progress' => 60,
                'image' => 'https://via.placeholder.com/400x200?text=Course+1'
            ],
            (object)[
                'id' => 2,
                'title' => 'Intermediate Soroban',
                'description' => 'Take your Soroban skills to the next level',
                'progress' => 30,
                'image' => 'https://via.placeholder.com/400x200?text=Course+2'
            ],
        ];

        return view('courses.my-courses', compact('courses'));
    }

    /**
     * Enroll the user in a course.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function enroll($id)
    {
        // In a real application, you would create an enrollment record in the database
        // For now, we'll just redirect with a success message

        return redirect()->route('my-courses')->with('success', 'You have successfully enrolled in the course!');
    }
}