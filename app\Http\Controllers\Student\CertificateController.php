<?php

namespace App\Http\Controllers\Student;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class CertificateController extends Controller
{
    /**
     * Display a listing of student's earned certificates.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // List all certificates earned by the student
        return view('student.certificates.index');
    }

    /**
     * Download a certificate.
     *
     * @param  int  $id
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function download($id)
    {
        // Generate and download certificate PDF
        // Return file download response
    }
}