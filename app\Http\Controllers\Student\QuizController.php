<?php

namespace App\Http\Controllers\Student;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class QuizController extends Controller
{
    /**
     * Display a listing of available quizzes.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // List all quizzes available to the student
        return view('student.quizzes.index');
    }

    /**
     * Display the specified quiz.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        // Show quiz details and questions
        return view('student.quizzes.show');
    }

    /**
     * Submit a completed quiz.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function submit(Request $request, $id)
    {
        // Validate and process quiz submission
        // Calculate score and store results
        
        return redirect()->route('student.quizzes.index')
            ->with('success', 'Quiz submitted successfully!');
    }
}