<?php

namespace App\Http\Controllers\Teacher;

use App\Http\Controllers\Controller;
use App\Models\TeachingResource;
use App\Models\Course;
use App\Models\Lesson;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class TeacherResourceController extends Controller
{
    /**
     * Display a listing of teaching resources.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $resources = TeachingResource::where('user_id', auth()->id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);
            
        return view('teacher.resources.index', compact('resources'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $courses = Course::where('user_id', auth()->id())->get();
        return view('teacher.resources.create', compact('courses'));
    }

    /**
     * Store a newly created resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:video,pdf,document,link',
            'course_id' => 'nullable|exists:courses,id',
            'lesson_id' => 'nullable|exists:lessons,id',
            'access_level' => 'required|in:free,premium',
            'file' => 'required_unless:type,link|file|mimes:pdf,doc,docx,mp4,mov,ogg,webm|max:102400',
            'external_url' => 'required_if:type,link|url',
        ]);

        $path = null;
        
        if ($request->type === 'link') {
            $path = $request->external_url;
        } else {
            $path = $request->file('file')->store('resources', 'public');
        }

        TeachingResource::create([
            'title' => $request->title,
            'description' => $request->description,
            'type' => $request->type,
            'path' => $path,
            'course_id' => $request->course_id,
            'lesson_id' => $request->lesson_id,
            'user_id' => auth()->id(),
            'access_level' => $request->access_level,
            'is_published' => $request->has('is_published'),
        ]);

        return redirect()->route('teacher.resources.index')
            ->with('success', 'Resource created successfully!');
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $resource = TeachingResource::where('user_id', auth()->id())
            ->findOrFail($id);
            
        return view('teacher.resources.show', compact('resource'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $resource = TeachingResource::where('user_id', auth()->id())
            ->findOrFail($id);
        $courses = Course::where('user_id', auth()->id())->get();
        
        return view('teacher.resources.edit', compact('resource', 'courses'));
    }

    /**
     * Update the specified resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $resource = TeachingResource::where('user_id', auth()->id())
            ->findOrFail($id);
            
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:video,pdf,document,link',
            'course_id' => 'nullable|exists:courses,id',
            'lesson_id' => 'nullable|exists:lessons,id',
            'access_level' => 'required|in:free,premium',
            'file' => 'nullable|file|mimes:pdf,doc,docx,mp4,mov,ogg,webm|max:102400',
            'external_url' => 'required_if:type,link|url',
        ]);

        $data = $request->except(['file', 'external_url']);
        $data['is_published'] = $request->has('is_published');
        
        if ($request->type === 'link') {
            $data['path'] = $request->external_url;
        } elseif ($request->hasFile('file')) {
            // Delete old file if it's not an external URL
            if (!str_contains($resource->path, 'http')) {
                Storage::disk('public')->delete($resource->path);
            }
            
            $data['path'] = $request->file('file')->store('resources', 'public');
        }

        $resource->update($data);

        return redirect()->route('teacher.resources.index')
            ->with('success', 'Resource updated successfully!');
    }

    /**
     * Remove the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $resource = TeachingResource::where('user_id', auth()->id())
            ->findOrFail($id);
            
        // Delete the file if it's not an external URL
        if (!str_contains($resource->path, 'http')) {
            Storage::disk('public')->delete($resource->path);
        }
        
        $resource->delete();
        
        return redirect()->route('teacher.resources.index')
            ->with('success', 'Resource deleted successfully!');
    }
    
    public function getLessons(Request $request)
    {
        $lessons = Lesson::where('course_id', $request->course_id)
            ->where('user_id', auth()->id())
            ->get();
            
        return response()->json($lessons);
    }
}
