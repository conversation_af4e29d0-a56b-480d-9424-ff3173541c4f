<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Course extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'duration_weeks',
        'difficulty_level',
        'is_active',
        'created_by',
        'updated_by'
    ];

    public function levels()
    {
        return $this->hasMany(CourseLevel::class);
    }

    public function discounts()
    {
        return $this->hasMany(Discount::class);
    }

    // Audit relationships
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}