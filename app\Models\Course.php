<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Course extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'description'];

    public function levels()
    {
        return $this->hasMany(CourseLevel::class);
    }

    public function discounts()
    {
        return $this->hasMany(Discount::class);
    }
}