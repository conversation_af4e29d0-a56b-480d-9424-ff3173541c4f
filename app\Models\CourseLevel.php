<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class CourseLevel extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'course_id',
        'level_number',
        'title',
        'created_by',
        'updated_by',
    ];

    // Relationships
    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function lessons()
    {
        return $this->hasMany(Lesson::class);
    }

    public function exams()
    {
        return $this->hasMany(Exam::class, 'level_id');
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class, 'level_id');
    }

    public function payments()
    {
        return $this->hasMany(Payment::class, 'level_id');
    }

    public function groups()
    {
        return $this->hasMany(Group::class, 'level_id');
    }

    public function certificates()
    {
        return $this->hasMany(Certificate::class, 'level_id');
    }

    public function studentProgress()
    {
        return $this->hasMany(StudentProgress::class, 'level_id');
    }

    public function discounts()
    {
        return $this->hasMany(Discount::class, 'level_id');
    }

    // Audit relationships
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }
}
