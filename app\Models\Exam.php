<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Exam extends Model
{
    use HasFactory;

    protected $fillable = [
        'level_id',
        'title',
        'exam_type',
        'iframe_url',
        'time_limit',
        'max_attempts',
    ];

    protected $casts = [
        'time_limit' => 'integer',
        'max_attempts' => 'integer',
    ];

    // Relationships
    public function level()
    {
        return $this->belongsTo(CourseLevel::class, 'level_id');
    }

    public function studentExams()
    {
        return $this->hasMany(StudentExam::class);
    }
}
