<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Payment extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'level_id',
        'amount',
        'transaction_code',
        'transaction_pic',
        'subscription_id',
        'subscription_start_date',
        'subscription_end_date',
        'is_confirmed',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'subscription_start_date' => 'date',
        'subscription_end_date' => 'date',
        'is_confirmed' => 'boolean',
    ];

    // Relationships
    public function student()
    {
        return $this->belongsTo(User::class, 'student_id');
    }

    public function level()
    {
        return $this->belongsTo(CourseLevel::class, 'level_id');
    }

    public function subscription()
    {
        return $this->belongsTo(Subscription::class);
    }
}
