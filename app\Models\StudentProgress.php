<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StudentProgress extends Model
{
    use HasFactory;

    protected $fillable = [
        'student_id',
        'lesson_id',
        'level_id',
        'status',
        'completion_percentage',
        'completed_at',
    ];

    protected $casts = [
        'completion_percentage' => 'decimal:2',
        'completed_at' => 'datetime',
    ];

    // Relationships
    public function student()
    {
        return $this->belongsTo(User::class, 'student_id');
    }

    public function lesson()
    {
        return $this->belongsTo(Lesson::class);
    }

    public function level()
    {
        return $this->belongsTo(CourseLevel::class, 'level_id');
    }
}
