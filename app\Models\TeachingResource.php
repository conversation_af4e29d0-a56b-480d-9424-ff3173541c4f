<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TeachingResource extends Model
{
    use HasFactory;

    protected $fillable = [
        'title', 
        'description', 
        'type', 
        'path', 
        'course_id', 
        'lesson_id', 
        'user_id',
        'access_level',
        'is_published'
    ];

    public function course()
    {
        return $this->belongsTo(Course::class);
    }

    public function lesson()
    {
        return $this->belongsTo(Lesson::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getUrlAttribute()
    {
        if ($this->type === 'video' && !str_contains($this->path, 'http')) {
            return asset('storage/' . $this->path);
        } elseif ($this->type === 'pdf' || $this->type === 'document') {
            return asset('storage/' . $this->path);
        }
        
        return $this->path; // For external URLs
    }
}