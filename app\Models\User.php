<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    protected $fillable = [
        'first_name',
        'second_name',
        'email',
        'password',
        'age',
        'role',
        'age_group',
        'user_type',
        'account_status',
        'parteners_student',
        'company_id',
        'can_manage_whatsapp_links',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'can_manage_whatsapp_links' => 'boolean',
    ];

    // Relationships
    public function profile()
    {
        return $this->hasOne(UserProfile::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function subscriptions()
    {
        return $this->hasMany(Subscription::class, 'student_id');
    }

    public function payments()
    {
        return $this->hasMany(Payment::class, 'student_id');
    }

    public function studentExams()
    {
        return $this->hasMany(StudentExam::class, 'student_id');
    }

    public function studentProgress()
    {
        return $this->hasMany(StudentProgress::class, 'student_id');
    }

    public function teacherGroups()
    {
        return $this->hasMany(Group::class, 'teacher_id');
    }

    public function groupMemberships()
    {
        return $this->hasMany(GroupMember::class);
    }

    public function certificates()
    {
        return $this->hasMany(Certificate::class, 'student_id');
    }

    public function competitionParticipations()
    {
        return $this->hasMany(CompetitionParticipant::class);
    }

    public function wonCompetitions()
    {
        return $this->hasMany(Competition::class, 'winner_id');
    }

    public function notifications()
    {
        return $this->hasMany(Notification::class);
    }

    public function uploadedAttachments()
    {
        return $this->hasMany(Attachment::class, 'uploaded_by');
    }

    public function whatsappLinkHistory()
    {
        return $this->hasMany(WhatsappLinkHistory::class);
    }

    // Helper methods
    public function isAdmin()
    {
        return in_array($this->role, ['superAdmin', 'admin']);
    }

    public function isTeacher()
    {
        return $this->role === 'teacher';
    }

    public function isStudent()
    {
        return $this->role === 'student';
    }

    public function getFullNameAttribute()
    {
        return $this->first_name . ' ' . $this->second_name;
    }
}
