<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WhatsappLinkHistory extends Model
{
    use HasFactory;

    protected $table = 'whatsapp_link_history';

    protected $fillable = [
        'group_id',
        'user_id',
        'whatsapp_link',
    ];

    // Relationships
    public function group()
    {
        return $this->belongsTo(Group::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
