# Soroban Educational Platform - Database Schema Analysis

## Overview
This document analyzes the current Laravel migrations against the provided MySQL schema and identifies differences, missing elements, and recommendations.

## Current Status
- **Database Type**: Switched from SQLite to MySQL
- **Total Tables**: 21 main tables + 2 Laravel system tables (sessions, personal_access_tokens)
- **Migration Files**: 24 migration files exist

## Schema Comparison

### ✅ Tables Already Implemented (with Laravel migrations)
1. **companies** - ✅ Complete
2. **users** - ✅ Complete with all fields and relationships
3. **user_profiles** - ✅ Complete
4. **courses** - ✅ Complete
5. **course_levels** - ✅ Complete
6. **attachments** - ✅ Complete
7. **lessons** - ✅ Complete with proper foreign keys
8. **quizzes** - ✅ Complete with foreign key fix
9. **exams** - ✅ Complete
10. **student_exams** - ⚠️ Missing check constraint and assigned_at field
11. **student_progress** - ✅ Complete
12. **discounts** - ✅ Complete
13. **subscriptions** - ✅ Complete
14. **payments** - ✅ Complete
15. **groups** - ✅ Complete with WhatsApp features
16. **group_members** - ✅ Complete
17. **certificates** - ✅ Complete
18. **competitions** - ✅ Complete
19. **competition_participants** - ✅ Complete
20. **notifications** - ✅ Complete with WhatsApp notification type
21. **whatsapp_link_history** - ✅ Complete

### ⚠️ Missing Elements in Current Migrations

#### 1. Check Constraint in student_exams
**Missing**: 
```sql
CONSTRAINT chk_one_assessment CHECK (
    (quiz_id IS NOT NULL AND exam_id IS NULL) OR
    (quiz_id IS NULL AND exam_id IS NOT NULL)
)
```
**Status**: Added in new migration file

#### 2. assigned_at field in student_exams
**Missing**: `assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`
**Status**: Added in new migration file

#### 3. MySQL-specific Indexes
**Missing**: Some performance indexes from the original schema
**Status**: Added in new migration file

### 🔄 Database Relationships

#### Primary Relationships
1. **Users ↔ Companies**: users.company_id → companies.id
2. **Users ↔ User Profiles**: user_profiles.user_id → users.id (1:1)
3. **Courses ↔ Course Levels**: course_levels.course_id → courses.id (1:many)
4. **Course Levels ↔ Lessons**: lessons.course_level_id → course_levels.id (1:many)
5. **Lessons ↔ Quizzes**: quizzes.lesson_id → lessons.id (1:1)
6. **Lessons ↔ Attachments**: lessons.video_attachment_id, lessons.pdf_attachment_id → attachments.id
7. **Users ↔ Groups**: groups.teacher_id → users.id, group_members.user_id → users.id
8. **Groups ↔ Course Levels**: groups.level_id → course_levels.id

#### Assessment Relationships
1. **Student Exams**: Links students to both quizzes and exams
2. **Student Progress**: Tracks progress at lesson and level granularity
3. **Certificates**: Issued when students complete levels

#### Business Logic Relationships
1. **Subscriptions**: Students subscribe to course levels
2. **Payments**: Track subscription payments with transaction details
3. **Discounts**: Company-specific discounts for courses/levels

### 📊 Key Features Supported

#### 1. Multi-Role User System
- superAdmin, admin, teacher, student, guest roles
- Company affiliations for corporate training
- Age group categorization (kids/adults)

#### 2. Hierarchical Course Structure
- Courses → Levels (one, two, three) → Lessons
- Flexible attachment system (video, PDF, other materials)
- Integrated quiz system per lesson

#### 3. Assessment System
- Placement exams, lesson quizzes, final exams
- Grade tracking with attempt limits
- Progress monitoring at lesson and level granularity

#### 4. WhatsApp Integration
- Group management with WhatsApp links
- Link expiry tracking
- Link history for audit purposes
- User permissions for link management

#### 5. Business Features
- Subscription management with renewal tracking
- Payment processing with transaction codes
- Company discounts and corporate training
- Certificate generation upon completion

#### 6. Communication System
- Multi-type notifications (message, assignment, payment, general, whatsapp_invite)
- Competition system with participant tracking

## Setup Instructions

### Option 1: Using the Complete Schema (Recommended for fresh setup)
```bash
# Run the complete schema SQL file
mysql -u root -p123abody < database/complete_schema.sql
```

### Option 2: Using Laravel Migrations (Recommended for existing Laravel projects)
```bash
# Run all migrations
php artisan migrate

# Or run fresh migrations (drops all tables first)
php artisan migrate:fresh
```

### Option 3: Using Setup Scripts
```bash
# Windows Batch
setup_mysql.bat

# PowerShell
./setup_mysql.ps1
```

## Verification Steps

1. **Check Database Creation**:
   ```sql
   SHOW DATABASES;
   USE soroban_albark;
   SHOW TABLES;
   ```

2. **Verify Relationships**:
   ```sql
   SELECT 
       TABLE_NAME,
       COLUMN_NAME,
       CONSTRAINT_NAME,
       REFERENCED_TABLE_NAME,
       REFERENCED_COLUMN_NAME
   FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
   WHERE REFERENCED_TABLE_SCHEMA = 'soroban_albark';
   ```

3. **Check Constraints**:
   ```sql
   SELECT * FROM INFORMATION_SCHEMA.CHECK_CONSTRAINTS 
   WHERE CONSTRAINT_SCHEMA = 'soroban_albark';
   ```

## Next Steps

1. ✅ Database schema is complete and ready
2. 🔄 Run migrations or execute complete schema
3. 📝 Create seeders for initial data
4. 🧪 Write tests for database relationships
5. 🚀 Begin application development

## Notes
- All foreign key relationships are properly defined with appropriate CASCADE/SET NULL actions
- Indexes are optimized for common query patterns
- The schema supports both individual and corporate training scenarios
- WhatsApp integration is fully supported with proper link management
- Assessment system is flexible and supports multiple exam types
