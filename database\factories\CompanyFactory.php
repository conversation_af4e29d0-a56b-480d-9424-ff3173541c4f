<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Company>
 */
class CompanyFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $companyTypes = ['Educational Institution', 'Training Center', 'School', 'Academy', 'Learning Center'];

        return [
            'name' => $this->faker->company() . ' ' . $this->faker->randomElement(['Academy', 'Institute', 'Center', 'School']),
            'email' => $this->faker->unique()->companyEmail(),
            'phone' => $this->faker->phoneNumber(),
            'address' => $this->faker->address(),
            'contact_person' => $this->faker->name(),
            'company_type' => $this->faker->randomElement($companyTypes),
            'created_at' => $this->faker->dateTimeBetween('-2 years', 'now'),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the company is a large educational institution.
     */
    public function large(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => $this->faker->city() . ' ' . $this->faker->randomElement(['University', 'College', 'Institute']),
            'company_type' => 'Educational Institution',
        ]);
    }

    /**
     * Indicate that the company is a small training center.
     */
    public function small(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => $this->faker->firstName() . ' ' . $this->faker->randomElement(['Training Center', 'Academy', 'Learning Hub']),
            'company_type' => 'Training Center',
        ]);
    }
}
