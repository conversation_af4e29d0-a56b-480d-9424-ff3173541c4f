<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Course>
 */
class CourseFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $courseNames = [
            'Soroban Fundamentals',
            'Advanced Soroban Techniques',
            'Mental Math Mastery',
            'Abacus for Beginners',
            'Speed Calculation Training',
            'Mathematical Problem Solving',
            'Soroban Competition Preparation',
            'Business Math with Soroban',
            'Kids Mental Math',
            'Adult Learning Soroban'
        ];

        $descriptions = [
            'Learn the fundamentals of Soroban calculation techniques and build a strong foundation in mental mathematics.',
            'Master advanced Soroban methods and develop lightning-fast calculation skills for complex problems.',
            'Enhance your mental math abilities through systematic Soroban training and practice.',
            'Perfect introduction to the Japanese abacus for complete beginners of all ages.',
            'Intensive training program focused on developing speed and accuracy in calculations.',
            'Apply Soroban techniques to solve real-world mathematical problems efficiently.',
            'Comprehensive preparation course for Soroban competitions and examinations.',
            'Practical application of Soroban skills in business and professional environments.',
            'Fun and engaging Soroban course designed specifically for young learners.',
            'Structured learning program for adult students new to Soroban techniques.'
        ];

        return [
            'name' => $this->faker->randomElement($courseNames),
            'description' => $this->faker->randomElement($descriptions),
            'duration_weeks' => $this->faker->numberBetween(4, 24),
            'difficulty_level' => $this->faker->randomElement(['beginner', 'intermediate', 'advanced']),
            'is_active' => $this->faker->boolean(85), // 85% chance of being active
            'created_at' => $this->faker->dateTimeBetween('-2 years', 'now'),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the course is for beginners.
     */
    public function beginner(): static
    {
        return $this->state(fn (array $attributes) => [
            'difficulty_level' => 'beginner',
            'duration_weeks' => $this->faker->numberBetween(4, 8),
            'name' => $this->faker->randomElement([
                'Soroban Fundamentals',
                'Abacus for Beginners',
                'Introduction to Mental Math',
                'Basic Soroban Techniques'
            ]),
        ]);
    }

    /**
     * Indicate that the course is intermediate level.
     */
    public function intermediate(): static
    {
        return $this->state(fn (array $attributes) => [
            'difficulty_level' => 'intermediate',
            'duration_weeks' => $this->faker->numberBetween(8, 16),
            'name' => $this->faker->randomElement([
                'Intermediate Soroban Skills',
                'Mental Math Enhancement',
                'Speed Calculation Training',
                'Applied Soroban Techniques'
            ]),
        ]);
    }

    /**
     * Indicate that the course is advanced level.
     */
    public function advanced(): static
    {
        return $this->state(fn (array $attributes) => [
            'difficulty_level' => 'advanced',
            'duration_weeks' => $this->faker->numberBetween(12, 24),
            'name' => $this->faker->randomElement([
                'Advanced Soroban Mastery',
                'Competition Preparation',
                'Expert Mental Math',
                'Professional Soroban Skills'
            ]),
        ]);
    }

    /**
     * Indicate that the course is for kids.
     */
    public function forKids(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => $this->faker->randomElement([
                'Kids Soroban Fun',
                'Young Mathematicians',
                'Children\'s Mental Math',
                'Junior Abacus Masters'
            ]),
            'duration_weeks' => $this->faker->numberBetween(6, 12),
            'difficulty_level' => $this->faker->randomElement(['beginner', 'intermediate']),
        ]);
    }

    /**
     * Indicate that the course is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
