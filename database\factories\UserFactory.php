<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;

class UserFactory extends Factory
{
    protected $model = User::class;

    public function definition()
    {
        $age = $this->faker->numberBetween(6, 60);
        $ageGroup = $age < 18 ? 'kids' : 'adults';

        return [
            'first_name' => $this->faker->firstName,
            'second_name' => $this->faker->lastName,
            'email' => $this->faker->unique()->safeEmail,
            'password' => Hash::make('password123'),
            'age' => $age,
            'role' => $this->faker->randomElement(['teacher', 'student']), // Default to common roles
            'age_group' => $ageGroup,
            'user_type' => 'regular',
            'account_status' => 'active',
            'can_manage_whatsapp_links' => false,
            'email_verified_at' => $this->faker->optional(0.8)->dateTimeBetween('-1 year', 'now'),
            'created_at' => $this->faker->dateTimeBetween('-2 years', 'now'),
            'updated_at' => now(),
        ];
    }

    /**
     * Indicate that the user is a super admin.
     */
    public function superAdmin(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'superAdmin',
            'age_group' => 'adults',
            'age' => $this->faker->numberBetween(25, 50),
            'can_manage_whatsapp_links' => true,
            'email_verified_at' => now(),
        ]);
    }

    /**
     * Indicate that the user is an admin.
     */
    public function admin(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'admin',
            'age_group' => 'adults',
            'age' => $this->faker->numberBetween(22, 45),
            'can_manage_whatsapp_links' => true,
            'email_verified_at' => now(),
        ]);
    }

    /**
     * Indicate that the user is a teacher.
     */
    public function teacher(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'teacher',
            'age_group' => 'adults',
            'age' => $this->faker->numberBetween(22, 60),
            'can_manage_whatsapp_links' => false,
            'email_verified_at' => now(),
        ]);
    }

    /**
     * Indicate that the user is a student.
     */
    public function student(): static
    {
        $age = $this->faker->numberBetween(6, 25);
        return $this->state(fn (array $attributes) => [
            'role' => 'student',
            'age_group' => $age < 18 ? 'kids' : 'adults',
            'age' => $age,
            'can_manage_whatsapp_links' => false,
        ]);
    }

    /**
     * Indicate that the user is a guest.
     */
    public function guest(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'guest',
            'can_manage_whatsapp_links' => false,
            'email_verified_at' => null,
        ]);
    }

    /**
     * Indicate that the user is a company user.
     */
    public function company(): static
    {
        return $this->state(fn (array $attributes) => [
            'user_type' => 'company',
            'role' => $this->faker->randomElement(['admin', 'teacher']),
            'age_group' => 'adults',
            'age' => $this->faker->numberBetween(25, 55),
        ]);
    }

    /**
     * Indicate that the user is a kid.
     */
    public function kid(): static
    {
        return $this->state(fn (array $attributes) => [
            'age' => $this->faker->numberBetween(6, 17),
            'age_group' => 'kids',
            'role' => 'student',
        ]);
    }

    /**
     * Indicate that the user account is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'account_status' => 'inactive',
        ]);
    }

    /**
     * Indicate that the user email is unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }
}