<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class UserFactory extends Factory
{
    protected $model = User::class;

    public function definition()
    {
        return [
            'first_name' => $this->faker->firstName,
            'second_name' => $this->faker->lastName,
            'email' => $this->faker->unique()->safeEmail,
            'password' => bcrypt('password'),
            'age' => $this->faker->numberBetween(6, 60),
            'role' => $this->faker->randomElement(['superAdmin', 'admin', 'teacher', 'student', 'guest']),
            'age_group' => $this->faker->randomElement(['kids', 'adults']),
            'user_type' => $this->faker->randomElement(['regular', 'company']),
            'account_status' => 'active',
        ];
    }
}