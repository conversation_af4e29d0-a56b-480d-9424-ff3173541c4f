<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('first_name');
            $table->string('second_name');
            $table->string('email')->unique();
            $table->string('password');
            $table->integer('age');
            $table->enum('role', ['superAdmin', 'admin', 'teacher', 'student', 'guest']);
            $table->enum('age_group', ['kids', 'adults'])->nullable();
            $table->enum('user_type', ['regular', 'company'])->default('regular');
            $table->enum('account_status', ['active', 'inactive'])->default('active');
            $table->string('parteners_student')->nullable()->comment('optional and contain name of the company or teacher advice student to it');
            $table->unsignedBigInteger('company_id')->nullable();
            $table->boolean('can_manage_whatsapp_links')->default(false)->comment('indicates if user can manage WhatsApp group links');
            $table->timestamps();

            $table->foreign('company_id')->references('id')->on('companies')->onDelete('set null');
            $table->index('email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
