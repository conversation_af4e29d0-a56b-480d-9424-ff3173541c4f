<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lessons', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('course_level_id');
            $table->integer('day_number');
            $table->integer('sequence');
            $table->string('title')->nullable();
            $table->unsignedBigInteger('video_attachment_id')->nullable();
            $table->unsignedBigInteger('pdf_attachment_id')->nullable();
            $table->unsignedBigInteger('quiz_id')->nullable();
            $table->timestamps();
            
            $table->foreign('course_level_id')->references('id')->on('course_levels')->onDelete('cascade');
            $table->foreign('video_attachment_id')->references('id')->on('attachments')->onDelete('set null');
            $table->foreign('pdf_attachment_id')->references('id')->on('attachments')->onDelete('set null');
            $table->foreign('quiz_id')->references('id')->on('quizzes')->onDelete('set null');
            $table->index('course_level_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lessons');
    }
};
