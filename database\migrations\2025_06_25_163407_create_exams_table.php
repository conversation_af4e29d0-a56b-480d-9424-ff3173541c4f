<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('exams', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('level_id')->nullable();
            $table->string('title')->nullable();
            $table->enum('exam_type', ['placement', 'final']);
            $table->text('iframe_url');
            $table->integer('time_limit')->nullable();
            $table->integer('max_attempts')->default(1);
            $table->timestamps();
            
            $table->foreign('level_id')->references('id')->on('course_levels')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('exams');
    }
};
