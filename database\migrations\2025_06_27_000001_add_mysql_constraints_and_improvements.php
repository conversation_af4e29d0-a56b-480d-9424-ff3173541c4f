<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add check constraint to student_exams table for MySQL
        if (Schema::hasTable('student_exams')) {
            DB::statement('ALTER TABLE student_exams ADD CONSTRAINT chk_one_assessment CHECK (
                (quiz_id IS NOT NULL AND exam_id IS NULL) OR
                (quiz_id IS NULL AND exam_id IS NOT NULL)
            )');
        }

        // Add assigned_at column to student_exams if it doesn't exist
        if (Schema::hasTable('student_exams') && !Schema::hasColumn('student_exams', 'assigned_at')) {
            Schema::table('student_exams', function (Blueprint $table) {
                $table->timestamp('assigned_at')->default(DB::raw('CURRENT_TIMESTAMP'))->after('attempt_number');
            });
        }

        // Ensure all required indexes exist
        if (Schema::hasTable('users')) {
            Schema::table('users', function (Blueprint $table) {
                if (!$this->indexExists('users', 'users_email_index')) {
                    $table->index('email');
                }
            });
        }

        if (Schema::hasTable('course_levels')) {
            Schema::table('course_levels', function (Blueprint $table) {
                if (!$this->indexExists('course_levels', 'idx_course_levels_course_id')) {
                    $table->index('course_id', 'idx_course_levels_course_id');
                }
            });
        }

        if (Schema::hasTable('lessons')) {
            Schema::table('lessons', function (Blueprint $table) {
                if (!$this->indexExists('lessons', 'idx_lessons_course_level_id')) {
                    $table->index('course_level_id', 'idx_lessons_course_level_id');
                }
            });
        }

        if (Schema::hasTable('student_exams')) {
            Schema::table('student_exams', function (Blueprint $table) {
                if (!$this->indexExists('student_exams', 'idx_student_exams_student_id')) {
                    $table->index('student_id', 'idx_student_exams_student_id');
                }
            });
        }

        if (Schema::hasTable('group_members')) {
            Schema::table('group_members', function (Blueprint $table) {
                if (!$this->indexExists('group_members', 'idx_group_members_group_id')) {
                    $table->index('group_id', 'idx_group_members_group_id');
                }
            });
        }

        if (Schema::hasTable('notifications')) {
            Schema::table('notifications', function (Blueprint $table) {
                if (!$this->indexExists('notifications', 'idx_notifications_user_id')) {
                    $table->index('user_id', 'idx_notifications_user_id');
                }
            });
        }

        if (Schema::hasTable('groups')) {
            Schema::table('groups', function (Blueprint $table) {
                if (!$this->indexExists('groups', 'idx_groups_whatsapp_link')) {
                    $table->index('whatsapp_link', 'idx_groups_whatsapp_link');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove check constraint
        if (Schema::hasTable('student_exams')) {
            DB::statement('ALTER TABLE student_exams DROP CONSTRAINT IF EXISTS chk_one_assessment');
        }

        // Remove assigned_at column if it was added
        if (Schema::hasTable('student_exams') && Schema::hasColumn('student_exams', 'assigned_at')) {
            Schema::table('student_exams', function (Blueprint $table) {
                $table->dropColumn('assigned_at');
            });
        }

        // Remove indexes (Laravel will handle this automatically when dropping foreign keys)
    }

    /**
     * Check if an index exists on a table
     */
    private function indexExists(string $table, string $index): bool
    {
        $indexes = DB::select("SHOW INDEX FROM {$table}");
        foreach ($indexes as $idx) {
            if ($idx->Key_name === $index) {
                return true;
            }
        }
        return false;
    }
};
