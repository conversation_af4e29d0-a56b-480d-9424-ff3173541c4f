<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('lessons', function (Blueprint $table) {
            $table->text('youtube_playlist_url')->nullable();
            $table->string('youtube_playlist_id', 255)->nullable();
            $table->boolean('requires_payment')->default(true);
        });
    }

    public function down(): void
    {
        Schema::table('lessons', function (Blueprint $table) {
            $table->dropColumn(['youtube_playlist_url', 'youtube_playlist_id', 'requires_payment']);
        });
    }
};
