<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\UserProfile;
use App\Models\Company;
use App\Models\Course;
use App\Models\CourseLevel;
use App\Models\Lesson;
use App\Models\Quiz;
use App\Models\Exam;
use App\Models\Subscription;
use App\Models\StudentProgress;
use App\Models\Certificate;
use App\Models\Group;
use App\Models\GroupMember;
use App\Models\Notification;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class BasicDataSeeder extends Seeder
{
    public function run(): void
    {
        $this->command->info('🚀 Creating basic sample data...');

        // 1. Create Companies
        $this->command->info('🏢 Creating companies...');
        $companies = [];
        for ($i = 1; $i <= 5; $i++) {
            $companies[] = Company::create([
                'name' => "Educational Center {$i}",
                'contact_email' => "center{$i}@example.com",
                'contact_phone' => "+1234567890{$i}",
                'address' => "123 Education Street, City {$i}",
            ]);
        }
        $this->command->info("✅ Created " . count($companies) . " companies");

        // 2. Create Users
        $this->command->info('👥 Creating users...');

        // Create teachers
        $teachers = [];
        for ($i = 1; $i <= 5; $i++) {
            $teacher = User::create([
                'first_name' => "Teacher{$i}",
                'second_name' => "Soroban",
                'email' => "teacher{$i}@example.com",
                'password' => Hash::make('password123'),
                'age' => 30 + $i,
                'role' => 'teacher',
                'age_group' => 'adults',
                'user_type' => 'regular',
                'account_status' => 'active',
                'can_manage_whatsapp_links' => false,
            ]);

            UserProfile::create([
                'user_id' => $teacher->id,
                'phone' => "+1234567{$i}00",
                'birth_date' => '1990-01-01',
                'address' => "Teacher Address {$i}",
                'bio' => "Experienced Soroban instructor with expertise in mental math.",
            ]);

            $teachers[] = $teacher;
        }

        // Create students
        $students = [];
        for ($i = 1; $i <= 20; $i++) {
            $isKid = $i <= 10;
            $age = $isKid ? (8 + $i) : (20 + $i);

            $student = User::create([
                'first_name' => "Student{$i}",
                'second_name' => "Learner",
                'email' => "student{$i}@example.com",
                'password' => Hash::make('password123'),
                'age' => $age,
                'role' => 'student',
                'age_group' => $isKid ? 'kids' : 'adults',
                'user_type' => 'regular',
                'account_status' => 'active',
                'can_manage_whatsapp_links' => false,
            ]);

            UserProfile::create([
                'user_id' => $student->id,
                'phone' => "+1234567{$i}11",
                'parent_phone' => $isKid ? "+1234567{$i}22" : null,
                'birth_date' => $isKid ? '2015-01-01' : '2000-01-01',
                'address' => "Student Address {$i}",
                'bio' => $isKid ? "Young learner interested in math." : "Adult student learning Soroban.",
            ]);

            $students[] = $student;
        }

        $this->command->info("✅ Created " . count($teachers) . " teachers and " . count($students) . " students");

        // 3. Create Courses
        $this->command->info('📚 Creating courses...');
        $courses = [
            Course::create([
                'name' => 'Soroban Fundamentals',
                'description' => 'Learn the basics of Soroban calculation techniques.',
            ]),
            Course::create([
                'name' => 'Advanced Soroban Techniques',
                'description' => 'Master advanced Soroban methods and speed calculations.',
            ]),
            Course::create([
                'name' => 'Kids Mental Math',
                'description' => 'Fun and engaging Soroban course for children.',
            ]),
        ];
        $this->command->info("✅ Created " . count($courses) . " courses");

        // 4. Create Course Levels
        $this->command->info('🎯 Creating course levels...');
        $levels = [];
        foreach ($courses as $course) {
            $levelNumbers = ['one', 'two', 'three'];
            for ($i = 0; $i < 3; $i++) {
                $level = CourseLevel::create([
                    'course_id' => $course->id,
                    'level_number' => $levelNumbers[$i],
                    'title' => "Level " . ($i + 1),
                ]);
                $levels[] = $level;
            }
        }
        $this->command->info("✅ Created " . count($levels) . " course levels");

        // 5. Create Lessons
        $this->command->info('📖 Creating lessons...');
        $lessons = [];
        foreach ($levels as $level) {
            for ($i = 1; $i <= 5; $i++) {
                $lesson = Lesson::create([
                    'course_level_id' => $level->id,
                    'day_number' => $i,
                    'sequence' => $i,
                    'title' => "Lesson {$i}: Basic Techniques",
                ]);
                $lessons[] = $lesson;
            }
        }
        $this->command->info("✅ Created " . count($lessons) . " lessons");

        // 6. Create Subscriptions
        $this->command->info('💳 Creating subscriptions...');
        $subscriptions = [];
        foreach (array_slice($students, 0, 15) as $index => $student) {
            $level = $levels[$index % count($levels)];
            $company = $companies[$index % count($companies)];

            $subscription = Subscription::create([
                'student_id' => $student->id,
                'level_id' => $level->id,
                'company_id' => $company->id,
                'start_date' => now()->subDays(30),
                'end_date' => now()->addMonths(6),
                'renewal_status' => 'active',
            ]);
            $subscriptions[] = $subscription;
        }
        $this->command->info("✅ Created " . count($subscriptions) . " subscriptions");

        // 7. Create Student Progress
        $this->command->info('📈 Creating student progress...');
        $progressCount = 0;
        foreach ($subscriptions as $subscription) {
            $levelLessons = array_filter($lessons, function($lesson) use ($subscription) {
                return $lesson->course_level_id == $subscription->level_id;
            });

            foreach (array_slice($levelLessons, 0, 3) as $lesson) {
                StudentProgress::create([
                    'student_id' => $subscription->student_id,
                    'lesson_id' => $lesson->id,
                    'level_id' => $subscription->level_id,
                    'status' => 'completed',
                    'completion_percentage' => 100,
                    'completed_at' => now()->subDays(rand(1, 20)),
                ]);
                $progressCount++;
            }
        }
        $this->command->info("✅ Created {$progressCount} progress records");

        // 8. Create Groups
        $this->command->info('👥 Creating WhatsApp groups...');
        $groups = [];
        foreach (array_slice($levels, 0, 5) as $index => $level) {
            $teacher = $teachers[$index % count($teachers)];

            $group = Group::create([
                'level_id' => $level->id,
                'teacher_id' => $teacher->id,
                'name' => "{$level->course->name} - {$level->title} Group",
                'description' => "WhatsApp study group for {$level->course->name}",
                'is_active' => true,
                'start_date' => now()->subDays(10),
                'end_date' => now()->addMonths(6),
                'whatsapp_link' => 'https://chat.whatsapp.com/ABC123XYZ789',
                'whatsapp_link_expiry' => now()->addDays(30),
            ]);

            // Add teacher as member
            GroupMember::create([
                'group_id' => $group->id,
                'user_id' => $teacher->id,
                'joined_at' => $group->created_at,
            ]);

            // Add some students
            $groupStudents = array_filter($subscriptions, function($sub) use ($level) {
                return $sub->level_id == $level->id;
            });

            foreach (array_slice($groupStudents, 0, 5) as $subscription) {
                GroupMember::create([
                    'group_id' => $group->id,
                    'user_id' => $subscription->student_id,
                    'joined_at' => now()->subDays(rand(1, 10)),
                ]);
            }

            $groups[] = $group;
        }
        $this->command->info("✅ Created " . count($groups) . " WhatsApp groups");

        // 9. Create Notifications
        $this->command->info('🔔 Creating notifications...');
        $notificationCount = 0;
        foreach (array_slice($students, 0, 10) as $student) {
            Notification::create([
                'user_id' => $student->id,
                'title' => 'Welcome to Soroban-AlBarq!',
                'message' => 'Welcome to our platform! Start your Soroban learning journey today.',
                'type' => 'general',
                'is_read' => false,
            ]);
            $notificationCount++;
        }
        $this->command->info("✅ Created {$notificationCount} notifications");

        $this->command->info('🎉 Basic data seeding completed!');
        $this->command->info("📊 Summary:");
        $this->command->info("   • Companies: " . count($companies));
        $this->command->info("   • Teachers: " . count($teachers));
        $this->command->info("   • Students: " . count($students));
        $this->command->info("   • Courses: " . count($courses));
        $this->command->info("   • Levels: " . count($levels));
        $this->command->info("   • Lessons: " . count($lessons));
        $this->command->info("   • Subscriptions: " . count($subscriptions));
        $this->command->info("   • Groups: " . count($groups));
        $this->command->info("   • Progress Records: {$progressCount}");
        $this->command->info("   • Notifications: {$notificationCount}");
    }
}
