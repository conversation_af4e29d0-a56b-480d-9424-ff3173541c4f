<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\UserProfile;
use App\Models\Company;
use App\Models\Course;
use App\Models\CourseLevel;
use App\Models\Lesson;
use App\Models\Quiz;
use App\Models\Exam;
use App\Models\Subscription;
use App\Models\StudentProgress;
use App\Models\Certificate;
use App\Models\Group;
use App\Models\GroupMember;
use App\Models\Notification;
use App\Models\Attachment;
use App\Models\Video;
use App\Models\Payment;
use App\Models\Discount;
use App\Models\Competition;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class ComprehensiveDataSeeder extends Seeder
{
    public function run(): void
    {
        $this->command->info('📊 Creating comprehensive sample data...');

        // 1. Create Companies
        $this->command->info('🏢 Creating companies...');
        $companies = Company::factory(10)->create();
        $this->command->info("✅ Created {$companies->count()} companies");

        // 2. Create Users with different roles
        $this->command->info('👥 Creating users...');
        
        // Create admins
        $admins = User::factory(3)->admin()->create();
        foreach ($admins as $admin) {
            UserProfile::factory()->create(['user_id' => $admin->id]);
        }

        // Create teachers
        $teachers = User::factory(15)->teacher()->create();
        foreach ($teachers as $teacher) {
            UserProfile::factory()->create(['user_id' => $teacher->id]);
        }

        // Create students (mix of kids and adults)
        $students = collect();
        $students = $students->merge(User::factory(30)->student()->kid()->create());
        $students = $students->merge(User::factory(20)->student()->create());
        
        foreach ($students as $student) {
            UserProfile::factory()->create(['user_id' => $student->id]);
        }

        // Create company users
        $companyUsers = User::factory(8)->company()->create();
        foreach ($companyUsers as $user) {
            UserProfile::factory()->create(['user_id' => $user->id]);
        }

        $this->command->info("✅ Created users: {$admins->count()} admins, {$teachers->count()} teachers, {$students->count()} students, {$companyUsers->count()} company users");

        // 3. Create Courses
        $this->command->info('📚 Creating courses...');
        $courses = collect();
        $courses = $courses->merge(Course::factory(3)->beginner()->create());
        $courses = $courses->merge(Course::factory(3)->intermediate()->create());
        $courses = $courses->merge(Course::factory(2)->advanced()->create());
        $courses = $courses->merge(Course::factory(2)->forKids()->create());
        
        $this->command->info("✅ Created {$courses->count()} courses");

        // 4. Create Course Levels
        $this->command->info('🎯 Creating course levels...');
        $levels = collect();
        foreach ($courses as $course) {
            $levelCount = rand(2, 4);
            for ($i = 1; $i <= $levelCount; $i++) {
                $level = CourseLevel::create([
                    'course_id' => $course->id,
                    'title' => "Level {$i}",
                    'description' => "Level {$i} of {$course->name} - " . $this->getLevelDescription($i),
                    'order' => $i,
                    'price' => rand(50, 200),
                    'duration_weeks' => rand(4, 12),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                $levels->push($level);
            }
        }
        $this->command->info("✅ Created {$levels->count()} course levels");

        // 5. Create Lessons
        $this->command->info('📖 Creating lessons...');
        $lessons = collect();
        foreach ($levels as $level) {
            $lessonCount = rand(5, 12);
            for ($i = 1; $i <= $lessonCount; $i++) {
                $lesson = Lesson::create([
                    'course_level_id' => $level->id,
                    'title' => "Lesson {$i}: " . $this->getLessonTitle(),
                    'content' => $this->getLessonContent(),
                    'order' => $i,
                    'video_url' => $this->getYouTubeUrl(),
                    'youtube_playlist_url' => $this->getPlaylistUrl(),
                    'youtube_playlist_id' => $this->getPlaylistId(),
                    'requires_payment' => rand(0, 1),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                $lessons->push($lesson);
            }
        }
        $this->command->info("✅ Created {$lessons->count()} lessons");

        // 6. Create Quizzes and Exams
        $this->command->info('📝 Creating quizzes and exams...');
        $quizzes = collect();
        $exams = collect();
        
        foreach ($levels as $level) {
            // Create 2-3 quizzes per level
            for ($i = 1; $i <= rand(2, 3); $i++) {
                $quiz = Quiz::create([
                    'level_id' => $level->id,
                    'title' => "Quiz {$i} - {$level->title}",
                    'description' => "Practice quiz for {$level->title}",
                    'google_form_url' => $this->getGoogleFormUrl(),
                    'google_form_embed_code' => $this->getGoogleFormEmbed(),
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                $quizzes->push($quiz);
            }

            // Create 1 exam per level
            $exam = Exam::create([
                'level_id' => $level->id,
                'title' => "Final Exam - {$level->title}",
                'description' => "Comprehensive exam for {$level->title}",
                'google_form_url' => $this->getGoogleFormUrl(),
                'google_form_embed_code' => $this->getGoogleFormEmbed(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            $exams->push($exam);
        }
        
        $this->command->info("✅ Created {$quizzes->count()} quizzes and {$exams->count()} exams");

        // 7. Create Subscriptions
        $this->command->info('💳 Creating subscriptions...');
        $subscriptions = collect();
        foreach ($students->take(40) as $student) {
            $randomLevels = $levels->random(rand(1, 3));
            foreach ($randomLevels as $level) {
                $startDate = now()->subDays(rand(1, 180));
                $subscription = Subscription::create([
                    'student_id' => $student->id,
                    'level_id' => $level->id,
                    'company_id' => $companies->random()->id,
                    'start_date' => $startDate,
                    'end_date' => $startDate->copy()->addMonths(rand(3, 12)),
                    'renewal_status' => $this->faker->randomElement(['active', 'expired', 'cancelled']),
                    'created_at' => $startDate,
                    'updated_at' => now(),
                ]);
                $subscriptions->push($subscription);
            }
        }
        $this->command->info("✅ Created {$subscriptions->count()} subscriptions");

        // 8. Create Student Progress
        $this->command->info('📈 Creating student progress...');
        $progressRecords = collect();
        foreach ($subscriptions->where('renewal_status', 'active') as $subscription) {
            $levelLessons = $lessons->where('course_level_id', $subscription->level_id);
            $completedCount = rand(0, $levelLessons->count());
            
            foreach ($levelLessons->take($completedCount) as $lesson) {
                $progress = StudentProgress::create([
                    'student_id' => $subscription->student_id,
                    'lesson_id' => $lesson->id,
                    'level_id' => $subscription->level_id,
                    'status' => $this->faker->randomElement(['completed', 'in_progress']),
                    'completion_percentage' => rand(70, 100),
                    'completed_at' => $this->faker->optional(0.7)->dateTimeBetween($subscription->start_date, 'now'),
                    'created_at' => $subscription->start_date,
                    'updated_at' => now(),
                ]);
                $progressRecords->push($progress);
            }
        }
        $this->command->info("✅ Created {$progressRecords->count()} progress records");

        // 9. Create Certificates
        $this->command->info('🏆 Creating certificates...');
        $certificates = collect();
        $eligibleStudents = $progressRecords->groupBy(['student_id', 'level_id'])
            ->filter(function ($levelProgress) {
                $completedCount = $levelProgress->where('status', 'completed')->count();
                $totalCount = $levelProgress->count();
                return $totalCount > 0 && ($completedCount / $totalCount) >= 0.8;
            });

        foreach ($eligibleStudents as $studentId => $levels) {
            foreach ($levels as $levelId => $progress) {
                $certificate = Certificate::create([
                    'student_id' => $studentId,
                    'level_id' => $levelId,
                    'issued_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
                ]);
                $certificates->push($certificate);
            }
        }
        $this->command->info("✅ Created {$certificates->count()} certificates");

        // 10. Create Groups
        $this->command->info('👥 Creating WhatsApp groups...');
        $groups = collect();
        foreach ($levels->take(15) as $level) {
            $teacher = $teachers->random();
            $group = Group::create([
                'level_id' => $level->id,
                'teacher_id' => $teacher->id,
                'name' => "{$level->course->name} - {$level->title} Group",
                'description' => "WhatsApp group for {$level->course->name} - {$level->title} students",
                'is_active' => $this->faker->boolean(90),
                'start_date' => now()->subDays(rand(1, 90)),
                'end_date' => now()->addDays(rand(90, 365)),
                'whatsapp_link' => $this->getWhatsAppLink(),
                'whatsapp_link_expiry' => now()->addDays(rand(30, 90)),
                'created_at' => now()->subDays(rand(1, 90)),
                'updated_at' => now(),
            ]);
            $groups->push($group);

            // Add teacher as member
            GroupMember::create([
                'group_id' => $group->id,
                'user_id' => $teacher->id,
                'joined_at' => $group->created_at,
            ]);

            // Add students with subscriptions to this level
            $levelStudents = $subscriptions->where('level_id', $level->id)
                ->where('renewal_status', 'active')
                ->pluck('student_id')
                ->unique()
                ->take(rand(5, 15));

            foreach ($levelStudents as $studentId) {
                GroupMember::create([
                    'group_id' => $group->id,
                    'user_id' => $studentId,
                    'joined_at' => $this->faker->dateTimeBetween($group->created_at, 'now'),
                ]);
            }
        }
        $this->command->info("✅ Created {$groups->count()} WhatsApp groups");

        // 11. Create Notifications
        $this->command->info('🔔 Creating notifications...');
        $notifications = collect();
        $allUsers = $students->merge($teachers)->merge($admins);
        
        foreach ($allUsers->take(30) as $user) {
            for ($i = 0; $i < rand(1, 5); $i++) {
                $notification = Notification::create([
                    'user_id' => $user->id,
                    'title' => $this->getNotificationTitle(),
                    'message' => $this->getNotificationMessage(),
                    'type' => $this->faker->randomElement(['general', 'whatsapp_invite', 'certificate', 'progress']),
                    'is_read' => $this->faker->boolean(60),
                    'created_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
                    'updated_at' => now(),
                ]);
                $notifications->push($notification);
            }
        }
        $this->command->info("✅ Created {$notifications->count()} notifications");

        $this->command->info('🎉 Comprehensive data seeding completed!');
        $this->command->info("📊 Summary:");
        $this->command->info("   • Companies: {$companies->count()}");
        $this->command->info("   • Users: " . ($admins->count() + $teachers->count() + $students->count() + $companyUsers->count()));
        $this->command->info("   • Courses: {$courses->count()}");
        $this->command->info("   • Levels: {$levels->count()}");
        $this->command->info("   • Lessons: {$lessons->count()}");
        $this->command->info("   • Subscriptions: {$subscriptions->count()}");
        $this->command->info("   • Progress Records: {$progressRecords->count()}");
        $this->command->info("   • Certificates: {$certificates->count()}");
        $this->command->info("   • Groups: {$groups->count()}");
        $this->command->info("   • Notifications: {$notifications->count()}");
    }

    private function getLevelDescription($level): string
    {
        $descriptions = [
            1 => "Introduction and basic concepts",
            2 => "Intermediate techniques and practice",
            3 => "Advanced methods and applications",
            4 => "Expert level and competition preparation"
        ];
        
        return $descriptions[$level] ?? "Advanced level training";
    }

    private function getLessonTitle(): string
    {
        $titles = [
            "Basic Addition Techniques",
            "Subtraction Methods",
            "Multiplication Fundamentals",
            "Division Strategies",
            "Mental Math Exercises",
            "Speed Calculation Practice",
            "Problem Solving Techniques",
            "Advanced Calculations",
            "Competition Preparation",
            "Real-world Applications"
        ];
        
        return $this->faker->randomElement($titles);
    }

    private function getLessonContent(): string
    {
        return "This lesson covers important Soroban techniques and provides comprehensive practice exercises. Students will learn step-by-step methods and develop their mental calculation skills through guided practice and interactive exercises.";
    }

    private function getYouTubeUrl(): string
    {
        $videoIds = ['dQw4w9WgXcQ', 'jNQXAC9IVRw', 'y6120QOlsfU', 'kJQP7kiw5Fk', 'L_jWHffIx5E'];
        return 'https://www.youtube.com/watch?v=' . $this->faker->randomElement($videoIds);
    }

    private function getPlaylistUrl(): string
    {
        $playlistIds = ['PLrAXtmRdnEQy4Qy9pMjNuFkSF2Q8CH7mQ', 'PLrAXtmRdnEQy4Qy9pMjNuFkSF2Q8CH7mR'];
        return 'https://www.youtube.com/playlist?list=' . $this->faker->randomElement($playlistIds);
    }

    private function getPlaylistId(): string
    {
        return 'PLrAXtmRdnEQy4Qy9pMjNuFkSF2Q8CH7m' . $this->faker->randomLetter();
    }

    private function getGoogleFormUrl(): string
    {
        return 'https://forms.gle/' . $this->faker->regexify('[A-Za-z0-9]{10}');
    }

    private function getGoogleFormEmbed(): string
    {
        $formId = $this->faker->regexify('[A-Za-z0-9_-]{44}');
        return '<iframe src="https://docs.google.com/forms/d/e/' . $formId . '/viewform?embedded=true" width="640" height="480" frameborder="0" marginheight="0" marginwidth="0">Loading…</iframe>';
    }

    private function getWhatsAppLink(): string
    {
        return 'https://chat.whatsapp.com/' . $this->faker->regexify('[A-Za-z0-9]{22}');
    }

    private function getNotificationTitle(): string
    {
        $titles = [
            "Welcome to Soroban-AlBarq!",
            "New lesson available",
            "Certificate earned!",
            "Group invitation",
            "Progress update",
            "Quiz reminder",
            "Exam scheduled",
            "Payment confirmation",
            "Course completion"
        ];
        
        return $this->faker->randomElement($titles);
    }

    private function getNotificationMessage(): string
    {
        $messages = [
            "Welcome to our Soroban learning platform! Start your journey today.",
            "A new lesson has been added to your course. Check it out now!",
            "Congratulations! You've earned a new certificate.",
            "You've been invited to join a WhatsApp study group.",
            "Great progress! You've completed another lesson.",
            "Don't forget to take your quiz before the deadline.",
            "Your exam is scheduled for next week. Good luck!",
            "Your payment has been confirmed. Access granted.",
            "Congratulations on completing your course!"
        ];
        
        return $this->faker->randomElement($messages);
    }

    private $faker;

    public function __construct()
    {
        $this->faker = \Faker\Factory::create();
    }
}
