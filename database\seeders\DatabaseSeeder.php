<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->command->info('🚀 Starting Soroban-AlBarq Database Seeding...');

        // Core system data
        $this->call([
            SuperAdminSeeder::class,
            BasicDataSeeder::class,
        ]);

        $this->command->info('✅ Database seeding completed successfully!');
        $this->command->info('🎯 Your Soroban-AlBarq platform is ready with sample data!');
    }
}

