<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class SuperAdminSeeder extends Seeder
{
    public function run(): void
    {
        User::create([
            'first_name' => 'Super',
            'second_name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('superadmin123'),
            'role' => 'superAdmin',
            'age_group' => 'adults',
            'age' => 30,
            'user_type' => 'regular',
            'account_status' => 'active',
        ]);
    }
}