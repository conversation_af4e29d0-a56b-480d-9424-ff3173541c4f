# 🏭 Factories and Seeders Guide - Soroban-AlBarq Platform

## 🎯 Overview

This guide covers the comprehensive factories and seeders created for the Soroban-AlBarq educational platform. These tools will help you populate your database with realistic sample data for development and testing.

## 📋 What's Been Created

### ✅ **Factories (5 Complete)**

1. **CompanyFactory** - Educational institutions and training centers
2. **UserFactory** - Users with all roles (superAdmin, admin, teacher, student, guest)
3. **CourseFactory** - Soroban courses with different difficulty levels
4. **UserProfileFactory** - User profiles with contact information
5. **CourseLevelFactory** - Course levels (created but needs implementation)

### ✅ **Seeders (3 Complete)**

1. **SuperAdminSeeder** - Creates admin users and profiles
2. **BasicDataSeeder** - Creates comprehensive sample data
3. **ComprehensiveDataSeeder** - Advanced seeder using factories (needs factory completion)

## 🏭 Factory Details

### **1. CompanyFactory**

Creates educational companies with realistic data:

```php
// Basic usage
Company::factory()->create();

// Create multiple companies
Company::factory(10)->create();

// Create large educational institution
Company::factory()->large()->create();

// Create small training center
Company::factory()->small()->create();
```

**Generated Fields:**
- Company name with educational suffixes
- Professional email addresses
- Phone numbers and addresses
- Contact person names
- Company types (Educational Institution, Training Center, etc.)

### **2. UserFactory**

Creates users with different roles and realistic profiles:

```php
// Create random user
User::factory()->create();

// Create specific roles
User::factory()->superAdmin()->create();
User::factory()->admin()->create();
User::factory()->teacher()->create();
User::factory()->student()->create();
User::factory()->guest()->create();

// Create kids vs adults
User::factory()->kid()->create();
User::factory()->student()->kid()->create();

// Create company users
User::factory()->company()->create();

// Create inactive users
User::factory()->inactive()->create();

// Create unverified users
User::factory()->unverified()->create();
```

**Features:**
- ✅ **Age-appropriate grouping** (kids vs adults)
- ✅ **Role-based permissions** (WhatsApp management for admins)
- ✅ **Realistic age ranges** per role
- ✅ **Email verification** status
- ✅ **Account status** management

### **3. CourseFactory**

Creates Soroban courses with educational content:

```php
// Create random course
Course::factory()->create();

// Create by difficulty level
Course::factory()->beginner()->create();
Course::factory()->intermediate()->create();
Course::factory()->advanced()->create();

// Create kids courses
Course::factory()->forKids()->create();

// Create inactive courses
Course::factory()->inactive()->create();
```

**Generated Content:**
- Realistic Soroban course names
- Educational descriptions
- Appropriate duration (4-24 weeks)
- Difficulty levels (beginner, intermediate, advanced)

### **4. UserProfileFactory**

Creates detailed user profiles:

```php
// Create profile for existing user
UserProfile::factory()->create(['user_id' => $user->id]);

// Create child profile (with parent phone)
UserProfile::factory()->forChild()->create(['user_id' => $child->id]);

// Create adult profile
UserProfile::factory()->forAdult()->create(['user_id' => $adult->id]);
```

**Generated Fields:**
- Phone numbers (with parent phone for kids)
- Birth dates (age-appropriate)
- Addresses and biographical information

## 🌱 Seeder Details

### **1. SuperAdminSeeder**

Creates essential admin accounts:

```bash
php artisan db:seed --class=SuperAdminSeeder
```

**Creates:**
- ✅ **Super Admin** - `<EMAIL>` / `superadmin123`
- ✅ **Admin** - `<EMAIL>` / `admin123`
- ✅ **Teacher** - `<EMAIL>` / `teacher123`
- ✅ **Student** - `<EMAIL>` / `student123`

### **2. BasicDataSeeder**

Creates comprehensive sample data without factories:

```bash
php artisan db:seed --class=BasicDataSeeder
```

**Creates:**
- 🏢 **5 Companies** - Educational institutions
- 👥 **25 Users** - 5 teachers, 20 students (mix of kids/adults)
- 📚 **3 Courses** - Fundamentals, Advanced, Kids courses
- 🎯 **9 Course Levels** - 3 levels per course
- 📖 **45 Lessons** - 5 lessons per level
- 💳 **15 Subscriptions** - Active student enrollments
- 📈 **45 Progress Records** - Student learning progress
- 👥 **5 WhatsApp Groups** - With members and links
- 🔔 **10 Notifications** - Welcome messages

### **3. ComprehensiveDataSeeder**

Advanced seeder using factories (requires factory completion):

```bash
php artisan db:seed --class=ComprehensiveDataSeeder
```

**Would Create:**
- 🏢 **10 Companies** - Using CompanyFactory
- 👥 **100+ Users** - Using UserFactory with all roles
- 📚 **10 Courses** - Using CourseFactory
- 🎯 **30+ Levels** - Dynamic level creation
- 📖 **200+ Lessons** - With YouTube integration
- 💳 **50+ Subscriptions** - Realistic enrollment patterns
- 📈 **500+ Progress Records** - Detailed learning analytics
- 🏆 **20+ Certificates** - Based on completion
- 👥 **15+ Groups** - WhatsApp integration
- 🔔 **100+ Notifications** - Various types

## 🚀 Usage Instructions

### **Step 1: Database Setup**

1. **Start MySQL server** (XAMPP, WAMP, or standalone)
2. **Create database:**
   ```sql
   CREATE DATABASE soroban_albarq;
   ```
3. **Configure .env:**
   ```env
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=soroban_albarq
   DB_USERNAME=root
   DB_PASSWORD=your_password
   ```

### **Step 2: Run Migrations**

```bash
php artisan migrate
```

### **Step 3: Run Seeders**

**Option A: Run all seeders**
```bash
php artisan db:seed
```

**Option B: Run specific seeders**
```bash
# Essential admin accounts
php artisan db:seed --class=SuperAdminSeeder

# Comprehensive sample data
php artisan db:seed --class=BasicDataSeeder
```

**Option C: Fresh database with seeders**
```bash
php artisan migrate:fresh --seed
```

## 🎯 Factory Usage Examples

### **Creating Test Data**

```php
// In your tests or tinker
use App\Models\{User, Company, Course, UserProfile};

// Create a complete educational setup
$company = Company::factory()->large()->create();
$teacher = User::factory()->teacher()->create();
$students = User::factory(10)->student()->kid()->create();

// Create profiles for all users
$teacher->profile()->save(UserProfile::factory()->forAdult()->make());
$students->each(function($student) {
    $student->profile()->save(UserProfile::factory()->forChild()->make());
});

// Create courses
$course = Course::factory()->beginner()->create();
```

### **Seeding Specific Data**

```php
// Create 50 students
User::factory(50)->student()->create()->each(function($user) {
    UserProfile::factory()->create(['user_id' => $user->id]);
});

// Create 10 teachers
User::factory(10)->teacher()->create()->each(function($user) {
    UserProfile::factory()->forAdult()->create(['user_id' => $user->id]);
});

// Create courses for different levels
Course::factory(3)->beginner()->create();
Course::factory(2)->intermediate()->create();
Course::factory(1)->advanced()->create();
```

## 📊 Sample Data Overview

After running `BasicDataSeeder`, you'll have:

### **User Accounts:**
| Role | Count | Email Pattern | Password |
|------|-------|---------------|----------|
| Super Admin | 1 | <EMAIL> | superadmin123 |
| Admin | 1 | <EMAIL> | admin123 |
| Teacher | 6 | teacher{1-5}@soroban.com | password123 |
| Student | 21 | student{1-20}@soroban.com | password123 |

### **Educational Content:**
- **3 Courses** - Fundamentals, Advanced, Kids
- **9 Levels** - 3 levels per course
- **45 Lessons** - With YouTube integration
- **15 Active Subscriptions** - Students enrolled in levels
- **5 WhatsApp Groups** - With real links and members

### **Business Data:**
- **5 Companies** - Educational institutions
- **45 Progress Records** - Student learning analytics
- **10 Notifications** - Welcome and system messages

## 🔧 Customization

### **Modify Factory Data**

Edit the factory files to customize generated data:

```php
// database/factories/CourseFactory.php
$courseNames = [
    'Your Custom Course Name',
    'Another Course Title',
    // Add your course names
];
```

### **Extend Seeders**

Add more data to seeders:

```php
// In BasicDataSeeder.php
// Add more companies, users, courses, etc.
```

### **Create Custom Factories**

```bash
php artisan make:factory LessonFactory
php artisan make:factory SubscriptionFactory
php artisan make:factory GroupFactory
```

## 🎉 Benefits

### **For Development:**
- ✅ **Realistic test data** for frontend development
- ✅ **Complete user workflows** testing
- ✅ **API endpoint** validation
- ✅ **Performance testing** with substantial data

### **For Testing:**
- ✅ **Unit test** data generation
- ✅ **Integration test** scenarios
- ✅ **User acceptance** testing
- ✅ **Load testing** preparation

### **For Demos:**
- ✅ **Client presentations** with real data
- ✅ **Feature demonstrations**
- ✅ **Training environments**
- ✅ **Stakeholder reviews**

## 🚀 Next Steps

1. **Complete remaining factories** (Lesson, Subscription, Group, etc.)
2. **Add more realistic data** patterns
3. **Create specialized seeders** for different scenarios
4. **Add data relationships** validation
5. **Create production-safe** seeders

Your Soroban-AlBarq platform now has professional-grade factories and seeders for comprehensive data management! 🎯
