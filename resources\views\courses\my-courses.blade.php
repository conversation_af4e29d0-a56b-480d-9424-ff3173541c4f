<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('My Courses') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(session('success'))
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
                    <span class="block sm:inline">{{ session('success') }}</span>
                </div>
            @endif

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-800 mb-4">Your Enrolled Courses</h2>
                    
                    @if(count($courses) > 0)
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            @foreach($courses as $course)
                                <div class="border rounded-lg overflow-hidden flex flex-col md:flex-row">
                                    <div class="md:w-1/3">
                                        <img src="{{ $course->image }}" alt="{{ $course->title }}" class="w-full h-full object-cover">
                                    </div>
                                    <div class="md:w-2/3 p-4">
                                        <h3 class="text-xl font-semibold mb-2">{{ $course->title }}</h3>
                                        <p class="text-gray-600 mb-4">{{ $course->description }}</p>
                                        
                                        <div class="mb-4">
                                            <div class="flex justify-between mb-1">
                                                <span class="text-sm font-medium text-gray-700">Progress</span>
                                                <span class="text-sm font-medium text-gray-700">{{ $course->progress }}%</span>
                                            </div>
                                            <div class="w-full bg-gray-200 rounded-full h-2.5">
                                                <div class="bg-blue-600 h-2.5 rounded-full" style="width: {{ $course->progress }}%"></div>
                                            </div>
                                        </div>
                                        
                                        <div class="flex justify-between">
                                            <a href="{{ route('courses.show', $course->id) }}" class="text-blue-500 hover:text-blue-700">
                                                View Course
                                            </a>
                                            <a href="#" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-sm">
                                                Continue Learning
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                            </svg>
                            <p class="text-gray-600 mb-4">You haven't enrolled in any courses yet.</p>
                            <a href="{{ route('courses.index') }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Browse Courses
                            </a>
                        </div>
                    @endif
                </div>
            </div>
            
            <!-- Recommended Courses -->
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Recommended For You</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <img src="https://via.placeholder.com/400x200?text=Recommended+Course+1" alt="Recommended Course" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2">Advanced Soroban Techniques</h3>
                        <p class="text-gray-600 mb-4">Take your Soroban skills to the next level with advanced calculation methods.</p>
                        <div class="flex justify-between items-center">
                            <span class="text-blue-500 font-bold">$89.99</span>
                            <a href="#" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-sm">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <img src="https://via.placeholder.com/400x200?text=Recommended+Course+2" alt="Recommended Course" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2">Mental Math Mastery</h3>
                        <p class="text-gray-600 mb-4">Learn powerful mental math techniques based on Soroban principles.</p>
                        <div class="flex justify-between items-center">
                            <span class="text-blue-500 font-bold">$69.99</span>
                            <a href="#" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-sm">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <img src="https://via.placeholder.com/400x200?text=Recommended+Course+3" alt="Recommended Course" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2">Soroban for Kids</h3>
                        <p class="text-gray-600 mb-4">A fun and engaging introduction to Soroban designed specifically for children.</p>
                        <div class="flex justify-between items-center">
                            <span class="text-blue-500 font-bold">$59.99</span>
                            <a href="#" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-sm">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>