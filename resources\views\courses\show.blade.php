<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ $course->title }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="flex flex-col md:flex-row">
                        <div class="md:w-2/3 p-4">
                            <img src="{{ $course->image }}" alt="{{ $course->title }}" class="w-full h-64 object-cover rounded-lg mb-6">
                            
                            <h1 class="text-3xl font-bold text-gray-800 mb-4">{{ $course->title }}</h1>
                            
                            <div class="flex items-center mb-6">
                                <div class="flex text-yellow-400 mr-2">
                                    @for ($i = 0; $i < 5; $i++)
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                        </svg>
                                    @endfor
                                </div>
                                <span class="text-gray-600">(4.8/5 - 24 reviews)</span>
                            </div>
                            
                            <h2 class="text-xl font-semibold text-gray-800 mb-2">Description</h2>
                            <p class="text-gray-600 mb-6">
                                {{ $course->description }}
                                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed euismod, nisl vel ultricies lacinia, 
                                nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl. Sed euismod, nisl vel ultricies lacinia,
                                nisl nisl aliquam nisl, eget aliquam nisl nisl sit amet nisl.
                            </p>
                            
                            <h2 class="text-xl font-semibold text-gray-800 mb-2">What You'll Learn</h2>
                            <ul class="list-disc list-inside text-gray-600 mb-6">
                                <li>Understand the basic structure and operation of the Soroban</li>
                                <li>Master addition and subtraction techniques</li>
                                <li>Develop mental calculation abilities</li>
                                <li>Improve concentration and memory</li>
                                <li>Solve complex arithmetic problems quickly</li>
                            </ul>
                            
                            <h2 class="text-xl font-semibold text-gray-800 mb-2">Course Content</h2>
                            <div class="mb-6">
                                <div class="border rounded-md">
                                    @foreach($course->lessons as $lesson)
                                        <div class="p-4 border-b last:border-b-0">
                                            <div class="flex justify-between items-center">
                                                <span class="font-medium">{{ $lesson->title }}</span>
                                                <span class="text-sm text-gray-500">15 min</span>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            
                            <h2 class="text-xl font-semibold text-gray-800 mb-2">Requirements</h2>
                            <ul class="list-disc list-inside text-gray-600 mb-6">
                                <li>No prior experience with Soroban is required</li>
                                <li>Basic understanding of arithmetic operations</li>
                                <li>A physical Soroban (can be purchased separately)</li>
                            </ul>
                        </div>
                        
                        <div class="md:w-1/3 p-4">
                            <div class="bg-gray-50 p-6 rounded-lg shadow-sm sticky top-8">
                                <div class="text-3xl font-bold text-gray-800 mb-4">${{ $course->price }}</div>
                                
                                @auth
                                    <a href="{{ route('courses.enroll', $course->id) }}" class="block w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-center mb-4">
                                        Enroll Now
                                    </a>
                                @else
                                    <a href="{{ route('login') }}" class="block w-full bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-center mb-4">
                                        Login to Enroll
                                    </a>
                                @endauth
                                
                                <div class="text-gray-600 text-sm mb-6">
                                    <p class="mb-2">This course includes:</p>
                                    <ul class="space-y-2">
                                        <li class="flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            10 hours of video content
                                        </li>
                                        <li class="flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            20 practice exercises
                                        </li>
                                        <li class="flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            Downloadable resources
                                        </li>
                                        <li class="flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            Full lifetime access
                                        </li>
                                        <li class="flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            Certificate of completion
                                        </li>
                                    </ul>
                                </div>
                                
                                <div class="flex justify-center space-x-4">
                                    <button class="text-gray-600 hover:text-gray-800">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                        </svg>
                                    </button>
                                    <button class="text-gray-600 hover:text-