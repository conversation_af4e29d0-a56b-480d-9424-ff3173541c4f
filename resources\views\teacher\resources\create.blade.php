<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Add New Teaching Resource') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <form action="{{ route('teacher.resources.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        
                        <div class="mb-4">
                            <label for="title" class="block text-gray-700 text-sm font-bold mb-2">Title:</label>
                            <input type="text" name="title" id="title" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" value="{{ old('title') }}" required>
                            @error('title')
                                <p class="text-red-500 text-xs italic mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="mb-4">
                            <label for="description" class="block text-gray-700 text-sm font-bold mb-2">Description:</label>
                            <textarea name="description" id="description" rows="4" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">{{ old('description') }}</textarea>
                            @error('description')
                                <p class="text-red-500 text-xs italic mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="mb-4">
                            <label for="type" class="block text-gray-700 text-sm font-bold mb-2">Resource Type:</label>
                            <select name="type" id="type" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                                <option value="">Select Type</option>
                                <option value="video" {{ old('type') == 'video' ? 'selected' : '' }}>Video</option>
                                <option value="pdf" {{ old('type') == 'pdf' ? 'selected' : '' }}>PDF</option>
                                <option value="document" {{ old('type') == 'document' ? 'selected' : '' }}>Document</option>
                                <option value="link" {{ old('type') == 'link' ? 'selected' : '' }}>External Link</option>
                            </select>
                            @error('type')
                                <p class="text-red-500 text-xs italic mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div id="file-upload" class="mb-4">
                            <label for="file" class="block text-gray-700 text-sm font-bold mb-2">Upload File:</label>
                            <input type="file" name="file" id="file" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                            <p class="text-gray-500 text-xs mt-1">Max file size: 100MB. Supported formats: PDF, DOC, DOCX, MP4, MOV, OGG, WEBM</p>
                            @error('file')
                                <p class="text-red-500 text-xs italic mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div id="external-url" class="mb-4 hidden">
                            <label for="external_url" class="block text-gray-700 text-sm font-bold mb-2">External URL:</label>
                            <input type="url" name="external_url" id="external_url" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" value="{{ old('external_url') }}">
                            <p class="text-gray-500 text-xs mt-1">For YouTube/Vimeo videos, use the embed URL</p>
                            @error('external_url')
                                <p class="text-red-500 text-xs italic mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="mb-4">
                            <label for="course_id" class="block text-gray-700 text-sm font-bold mb-2">Associated Course (Optional):</label>
                            <select name="course_id" id="course_id" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                                <option value="">None</option>
                                @foreach($courses as $course)
                                    <option value="{{ $course->id }}" {{ old('course_id') == $course->id ? 'selected' : '' }}>{{ $course->title }}</option>
                                @endforeach
                            </select>
                        </div>
                        
                        <div id="lesson-select" class="mb-4 {{ old('course_id') ? '' : 'hidden' }}">
                            <label for="lesson_id" class="block text-gray-700 text-sm font-bold mb-2">Associated Lesson (Optional):</label>
                            <select name="lesson_id" id="lesson_id" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                                <option value="">None</option>
                                <!-- Lessons will be loaded via AJAX -->
                            </select>
                        </div>
                        
                        <div class="mb-4">
                            <label for="access_level" class="block text-gray-700 text-sm font-bold mb-2">Access Level:</label>
                            <select name="access_level" id="access_level" class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" required>
                                <option value="free" {{ old('access_level') == 'free' ? 'selected' : '' }}>Free</option>
                                <option value="premium" {{ old('access_level') == 'premium' ? 'selected' : '' }}>Premium</option>
                            </select>
                        </div>
                        
                        <div class="mb-6">
                            <label class="flex items-center">
                                <input type="checkbox" name="is_published" class="form-checkbox" {{ old('is_published') ? 'checked' : '' }}>
                                <span class="ml-2 text-gray-700">Publish immediately</span>
                            </label>
                        </div>
                        
                        <div class="flex items-center justify-between">
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                                Create Resource
                            </button>
                            <a href="{{ route('teacher.resources.index') }}" class="text-gray-500 hover:text-gray-700">
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const typeSelect = document.getElementById('type');
            const fileUpload = document.getElementById('file-upload');
            const externalUrl = document.getElementById('external-url');
            const courseSelect = document.getElementById('course_id');
            const lessonSelect = document.getElementById('lesson-select');
            
            // Toggle file upload or external URL based on type
            typeSelect.addEventListener('change', function() {
                if (this.value === 'link') {
                    fileUpload.classList.add('hidden');
                    externalUrl.classList.remove('hidden');
                } else {
                    fileUpload.classList.remove('hidden');
                    externalUrl.classList.add('hidden');
                }
            });
            
            // Trigger change event to set initial state
            typeSelect.dispatchEvent(new Event('change'));
            
            // Load lessons when course is selected
            courseSelect.addEventListener('change', function() {
                if (this.value) {
                    lessonSelect.classList.remove('hidden');
                    fetchLessons(this.value);
                } else {
                    lessonSelect.classList.add('hidden');
                    document.getElementById('lesson_id').innerHTML = '<option value="">None</option>';
                }
            });
            
            // Fetch lessons for a course
            function fetchLessons(courseId) {
                fetch(`/teacher/courses/${courseId}/lessons`)
                    .then(response => response.json())
                    .then(data => {
                        let options = '<option value="">None</option>';
                        data.forEach(lesson => {
                            options += `<option value="${lesson.id}">${lesson.title}</option>`;
                        });
                        document.getElementById('lesson_id').innerHTML = options;
                    })
                    .catch(error => console.error('Error fetching lessons:', error));
            }
            
            // If course is already selected on page load, fetch lessons
            if (courseSelect.value) {
                fetchLessons(courseSelect.value);
            }
        });
    </script>
</x-app-layout>