<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ $resource->title }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('teacher.resources.edit', $resource->id) }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Edit Resource
                </a>
                <a href="{{ route('teacher.resources.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to List
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg mb-6">
                <div class="p-6 bg-white border-b border-gray-200">
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">Resource Details</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="text-gray-600"><span class="font-semibold">Type:</span> <span class="capitalize">{{ $resource->type }}</span></p>
                                <p class="text-gray-600"><span class="font-semibold">Access Level:</span> <span class="capitalize">{{ $resource->access_level }}</span></p>
                                <p class="text-gray-600"><span class="font-semibold">Status:</span> 
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $resource->is_published ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                        {{ $resource->is_published ? 'Published' : 'Draft' }}
                                    </span>
                                </p>
                            </div>
                            <div>
                                <p class="text-gray-600"><span class="font-semibold">Course:</span> {{ $resource->course->title ?? 'N/A' }}</p>
                                <p class="text-gray-600"><span class="font-semibold">Lesson:</span> {{ $resource->lesson->title ?? 'N/A' }}</p>
                                <p class="text-gray-600"><span class="font-semibold">Created:</span> {{ $resource->created_at->format('M d, Y') }}</p>
                            </div>
                        </div>
                    </div>
                    
                    @if($resource->description)
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-800 mb-2">Description</h3>
                            <div class="bg-gray-50 p-4 rounded">
                                {{ $resource->description }}
                            </div>
                        </div>
                    @endif
                    
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-2">Resource Preview</h3>
                        
                        @if($resource->type === 'video')
                            <div class="aspect-w-16 aspect-h-9">
                                @if(str_contains($resource->path, 'youtube.com') || str_contains($resource->path, 'youtu.be'))
                                    <iframe src="{{ $resource->url }}" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen class="w-full h-96"></iframe>
                                @elseif(str_contains($resource->path, 'vimeo.com'))
                                    <iframe src="{{ $resource->url }}" frameborder="0" allow="autoplay; fullscreen; picture-in-picture" allowfullscreen class="w-full h-96"></iframe>
                                @else
                                    <video controls class="w-full h-auto">
                                        <source src="{{ $resource->url }}" type="video/mp4">
                                        Your browser does not support the video tag.
                                    </video>
                                @endif
                            </div>
                        @elseif($resource->type === 'pdf')
                            <div class="border rounded">
                                <iframe src="{{ $resource->url }}" class="w-full h-96"></iframe>
                            </div>
                            <div class="mt-2">
                                <a href="{{ $resource->url }}" target="_blank" class="text-blue-500 hover:text-blue-700">
                                    Open PDF in new tab
                                </a>
                            </div>
                        @elseif($resource->type === 'document')
                            <div class="bg-gray-50 p-4 rounded flex items-center justify-between">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <span>{{ basename($resource->path) }}</span>
                                </div>
                                <a href="{{ $resource->url }}" download class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-sm">
                                    Download
                                </a>
                            </div>
                        @elseif($resource->type === 'link