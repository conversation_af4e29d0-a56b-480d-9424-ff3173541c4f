<?php

use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\CourseController;
// use App\Http\Controllers\Admin\LessonController;
// use App\Http\Controllers\Admin\ReportController;
// use App\Http\Controllers\Admin\SettingController;
use Illuminate\Support\Facades\Route;

// Admin routes - protected by auth and role middleware
Route::prefix('admin')->middleware(['auth', 'role:superAdmin,admin'])->name('admin.')->group(function () {
    // Admin dashboard
    Route::get('/', [AdminController::class, 'dashboard'])->name('dashboard');
    
    // User management
    Route::resource('users', UserController::class);
    Route::post('users/{user}/change-status', [UserController::class, 'changeStatus'])->name('users.change-status');
    
    // Course management
    Route::resource('courses', CourseController::class);
    Route::post('courses/{course}/publish', [CourseController::class, 'publish'])->name('courses.publish');
    Route::post('courses/{course}/unpublish', [CourseController::class, 'unpublish'])->name('courses.unpublish');
    
    // Lesson management (commented out until controller is created)
    // Route::resource('courses.lessons', LessonController::class);

    // Reports (commented out until controller is created)
    // Route::get('reports/users', [ReportController::class, 'users'])->name('reports.users');
    // Route::get('reports/courses', [ReportController::class, 'courses'])->name('reports.courses');
    // Route::get('reports/enrollments', [ReportController::class, 'enrollments'])->name('reports.enrollments');
    // Route::get('reports/revenue', [ReportController::class, 'revenue'])->name('reports.revenue');

    // Settings (commented out until controller is created)
    // Route::get('settings', [SettingController::class, 'index'])->name('settings');
    // Route::post('settings', [SettingController::class, 'update'])->name('settings.update');
});