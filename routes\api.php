<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\CourseController;
use App\Http\Controllers\Api\CourseLevelController;
use App\Http\Controllers\Api\LessonController;
use App\Http\Controllers\Api\QuizController;
use App\Http\Controllers\Api\ExamController;
use App\Http\Controllers\Api\StudentExamController;
use App\Http\Controllers\Api\StudentProgressController;
use App\Http\Controllers\Api\SubscriptionController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\GroupController;
use App\Http\Controllers\Api\GroupMemberController;
use App\Http\Controllers\Api\CertificateController;
use App\Http\Controllers\Api\CompetitionController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\AttachmentController;
use App\Http\Controllers\Api\CompanyController;
use App\Http\Controllers\Api\DiscountController;
use App\Http\Controllers\Api\VideoController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Test route
Route::get('/test', function () {
    return response()->json([
        'success' => true,
        'message' => 'API is working!',
        'timestamp' => now()
    ]);
});

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);
Route::post('/reset-password', [AuthController::class, 'resetPassword']);

// Email verification routes
Route::get('/email/verify/{id}/{hash}', [AuthController::class, 'verifyEmail'])
    ->middleware(['signed'])
    ->name('verification.verify');

Route::post('/email/verification-notification', [AuthController::class, 'sendVerificationEmail'])
    ->middleware(['auth:sanctum', 'throttle:6,1'])
    ->name('verification.send');

Route::get('/email/verify-status', [AuthController::class, 'checkEmailVerification'])
    ->middleware('auth:sanctum');

// Public course information
Route::get('/courses', [CourseController::class, 'index']);
Route::get('/courses/{course}', [CourseController::class, 'show']);
Route::get('/courses/{course}/levels', [CourseLevelController::class, 'index']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {

    // Auth routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [AuthController::class, 'user']);
    Route::put('/user/profile', [AuthController::class, 'updateProfile']);

    // User management
    Route::apiResource('users', UserController::class);
    Route::post('/users/{user}/change-status', [UserController::class, 'changeStatus']);

    // Course management
    Route::apiResource('courses', CourseController::class)->except(['index', 'show']);
    Route::apiResource('course-levels', CourseLevelController::class);

    // Lesson management
    Route::apiResource('lessons', LessonController::class);
    Route::get('/course-levels/{level}/lessons', [LessonController::class, 'getByLevel']);

    // Quiz management
    Route::apiResource('quizzes', QuizController::class);
    Route::get('/lessons/{lesson}/quiz', [QuizController::class, 'getByLesson']);

    // Exam management
    Route::apiResource('exams', ExamController::class);
    Route::get('/course-levels/{level}/exams', [ExamController::class, 'getByLevel']);

    // Student exam submissions
    Route::apiResource('student-exams', StudentExamController::class);
    Route::post('/student-exams/{studentExam}/submit', [StudentExamController::class, 'submit']);
    Route::get('/students/{student}/exams', [StudentExamController::class, 'getStudentExams']);

    // Student progress tracking
    Route::apiResource('student-progress', StudentProgressController::class);
    Route::get('/students/{student}/progress', [StudentProgressController::class, 'getStudentProgress']);
    Route::post('/student-progress/update-lesson', [StudentProgressController::class, 'updateLessonProgress']);

    // Subscription management
    Route::apiResource('subscriptions', SubscriptionController::class);
    Route::get('/students/{student}/subscriptions', [SubscriptionController::class, 'getStudentSubscriptions']);
    Route::post('/subscriptions/{subscription}/renew', [SubscriptionController::class, 'renew']);

    // Payment management
    Route::apiResource('payments', PaymentController::class);
    Route::post('/payments/{payment}/confirm', [PaymentController::class, 'confirm']);
    Route::get('/students/{student}/payments', [PaymentController::class, 'getStudentPayments']);

    // Group management
    Route::apiResource('groups', GroupController::class);
    Route::post('/groups/{group}/update-whatsapp-link', [GroupController::class, 'updateWhatsappLink']);
    Route::get('/teachers/{teacher}/groups', [GroupController::class, 'getTeacherGroups']);

    // Group member management
    Route::apiResource('group-members', GroupMemberController::class);
    Route::post('/groups/{group}/add-member', [GroupMemberController::class, 'addMember']);
    Route::delete('/groups/{group}/remove-member/{user}', [GroupMemberController::class, 'removeMember']);

    // Certificate management
    Route::apiResource('certificates', CertificateController::class);
    Route::get('/students/{student}/certificates', [CertificateController::class, 'getStudentCertificates']);
    Route::post('/certificates/issue', [CertificateController::class, 'issue']);

    // Competition management
    Route::apiResource('competitions', CompetitionController::class);
    Route::post('/competitions/{competition}/participate', [CompetitionController::class, 'participate']);
    Route::get('/competitions/{competition}/participants', [CompetitionController::class, 'getParticipants']);

    // Notification management
    Route::apiResource('notifications', NotificationController::class);
    Route::post('/notifications/{notification}/mark-read', [NotificationController::class, 'markAsRead']);
    Route::post('/notifications/mark-all-read', [NotificationController::class, 'markAllAsRead']);

    // File attachment management
    Route::apiResource('attachments', AttachmentController::class);
    Route::post('/attachments/upload', [AttachmentController::class, 'upload']);

    // Video management
    Route::get('/lessons/{lesson}/video', [VideoController::class, 'getPlaylistEmbed']);
    Route::get('/videos/my-lessons', [VideoController::class, 'getUserVideoLessons']);
    Route::get('/videos/stats', [VideoController::class, 'getVideoStats']);

    // Quiz and Exam embeds
    Route::get('/quizzes/{quiz}/embed', [QuizController::class, 'getQuizEmbed']);
    Route::get('/exams/{exam}/embed', [ExamController::class, 'getExamEmbed']);
    Route::get('/lessons/{lesson}/quiz', [QuizController::class, 'getByLesson']);
    Route::get('/levels/{level}/exams', [ExamController::class, 'getByLevel']);

    // Notification management
    Route::post('/notifications/bulk', [NotificationController::class, 'sendBulkNotification']);
    Route::post('/notifications/{notification}/read', [NotificationController::class, 'markAsRead']);
    Route::post('/notifications/read-all', [NotificationController::class, 'markAllAsRead']);

    // Student Progress management
    Route::post('/progress/lesson', [StudentProgressController::class, 'updateLessonProgress']);
    Route::get('/progress/my-progress', [StudentProgressController::class, 'getMyProgress']);
    Route::get('/progress/student/{student}', [StudentProgressController::class, 'getStudentProgress']);
    Route::get('/progress/level/{level}/stats', [StudentProgressController::class, 'getLevelStats']);

    // Certificate management
    Route::post('/certificates/issue', [CertificateController::class, 'issue']);
    Route::get('/certificates/my-certificates', [CertificateController::class, 'getMyCertificates']);
    Route::get('/certificates/student/{student}', [CertificateController::class, 'getStudentCertificates']);
    Route::post('/certificates/check-eligibility', [CertificateController::class, 'checkEligibility']);
    Route::get('/certificates/stats', [CertificateController::class, 'getCertificateStats']);

    // WhatsApp Group management
    Route::get('/groups/stats', [GroupController::class, 'getGroupStats']);
    Route::get('/groups/my-groups', [GroupController::class, 'getMyGroups']);
    Route::get('/groups/{group}/members', [GroupMemberController::class, 'getGroupMembers']);
    Route::post('/groups/{group}/bulk-add-members', [GroupMemberController::class, 'bulkAddMembers']);

    // Group member management
    Route::get('/group-members/group/{group}', [GroupMemberController::class, 'getGroupMembers']);

    // Company management (Admin only)
    Route::middleware('role:superAdmin,admin')->group(function () {
        Route::apiResource('companies', CompanyController::class);
        Route::apiResource('discounts', DiscountController::class);
    });

    // Admin routes
    Route::middleware('role:superAdmin,admin')->prefix('admin')->name('admin.')->group(function () {
        Route::get('/dashboard', [UserController::class, 'adminDashboard']);
        Route::get('/reports/users', [UserController::class, 'userReports']);
        Route::get('/reports/courses', [CourseController::class, 'courseReports']);
        Route::get('/reports/payments', [PaymentController::class, 'paymentReports']);
        Route::get('/reports/enrollments', [SubscriptionController::class, 'enrollmentReports']);
    });

    // Teacher routes
    Route::middleware('role:teacher')->prefix('teacher')->name('teacher.')->group(function () {
        Route::get('/dashboard', [UserController::class, 'teacherDashboard']);
        Route::get('/students', [UserController::class, 'getTeacherStudents']);
        Route::get('/my-groups', [GroupController::class, 'getMyGroups']);
    });

    // Student routes
    Route::middleware('role:student')->prefix('student')->name('student.')->group(function () {
        Route::get('/dashboard', [UserController::class, 'studentDashboard']);
        Route::get('/my-courses', [SubscriptionController::class, 'getMyCourses']);
        Route::get('/my-progress', [StudentProgressController::class, 'getMyProgress']);
        Route::get('/my-exams', [StudentExamController::class, 'getMyExams']);
        Route::get('/my-certificates', [CertificateController::class, 'getMyCertificates']);
        Route::get('/my-groups', [GroupController::class, 'getMyGroups']);
    });
});
