<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\CourseController;
use App\Http\Controllers\Api\CourseLevelController;
use App\Http\Controllers\Api\LessonController;
use App\Http\Controllers\Api\QuizController;
use App\Http\Controllers\Api\ExamController;
use App\Http\Controllers\Api\StudentExamController;
use App\Http\Controllers\Api\StudentProgressController;
use App\Http\Controllers\Api\SubscriptionController;
use App\Http\Controllers\Api\PaymentController;
use App\Http\Controllers\Api\GroupController;
use App\Http\Controllers\Api\GroupMemberController;
use App\Http\Controllers\Api\CertificateController;
use App\Http\Controllers\Api\CompetitionController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\Api\AttachmentController;
use App\Http\Controllers\Api\CompanyController;
use App\Http\Controllers\Api\DiscountController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Test route
Route::get('/test', function () {
    return response()->json([
        'success' => true,
        'message' => 'API is working!',
        'timestamp' => now()
    ]);
});

// Public routes
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/forgot-password', [AuthController::class, 'forgotPassword']);
Route::post('/reset-password', [AuthController::class, 'resetPassword']);

// Public course information
Route::get('/courses', [CourseController::class, 'index']);
Route::get('/courses/{course}', [CourseController::class, 'show']);
Route::get('/courses/{course}/levels', [CourseLevelController::class, 'index']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    
    // Auth routes
    Route::post('/logout', [AuthController::class, 'logout']);
    Route::get('/user', [AuthController::class, 'user']);
    Route::put('/user/profile', [AuthController::class, 'updateProfile']);
    
    // User management
    Route::apiResource('users', UserController::class);
    Route::post('/users/{user}/change-status', [UserController::class, 'changeStatus']);
    
    // Course management
    Route::apiResource('courses', CourseController::class)->except(['index', 'show']);
    Route::apiResource('course-levels', CourseLevelController::class);
    
    // Lesson management
    Route::apiResource('lessons', LessonController::class);
    Route::get('/course-levels/{level}/lessons', [LessonController::class, 'getByLevel']);
    
    // Quiz management
    Route::apiResource('quizzes', QuizController::class);
    Route::get('/lessons/{lesson}/quiz', [QuizController::class, 'getByLesson']);
    
    // Exam management
    Route::apiResource('exams', ExamController::class);
    Route::get('/course-levels/{level}/exams', [ExamController::class, 'getByLevel']);
    
    // Student exam submissions
    Route::apiResource('student-exams', StudentExamController::class);
    Route::post('/student-exams/{studentExam}/submit', [StudentExamController::class, 'submit']);
    Route::get('/students/{student}/exams', [StudentExamController::class, 'getStudentExams']);
    
    // Student progress tracking
    Route::apiResource('student-progress', StudentProgressController::class);
    Route::get('/students/{student}/progress', [StudentProgressController::class, 'getStudentProgress']);
    Route::post('/student-progress/update-lesson', [StudentProgressController::class, 'updateLessonProgress']);
    
    // Subscription management
    Route::apiResource('subscriptions', SubscriptionController::class);
    Route::get('/students/{student}/subscriptions', [SubscriptionController::class, 'getStudentSubscriptions']);
    Route::post('/subscriptions/{subscription}/renew', [SubscriptionController::class, 'renew']);
    
    // Payment management
    Route::apiResource('payments', PaymentController::class);
    Route::post('/payments/{payment}/confirm', [PaymentController::class, 'confirm']);
    Route::get('/students/{student}/payments', [PaymentController::class, 'getStudentPayments']);
    
    // Group management
    Route::apiResource('groups', GroupController::class);
    Route::post('/groups/{group}/update-whatsapp-link', [GroupController::class, 'updateWhatsappLink']);
    Route::get('/teachers/{teacher}/groups', [GroupController::class, 'getTeacherGroups']);
    
    // Group member management
    Route::apiResource('group-members', GroupMemberController::class);
    Route::post('/groups/{group}/add-member', [GroupMemberController::class, 'addMember']);
    Route::delete('/groups/{group}/remove-member/{user}', [GroupMemberController::class, 'removeMember']);
    
    // Certificate management
    Route::apiResource('certificates', CertificateController::class);
    Route::get('/students/{student}/certificates', [CertificateController::class, 'getStudentCertificates']);
    Route::post('/certificates/issue', [CertificateController::class, 'issue']);
    
    // Competition management
    Route::apiResource('competitions', CompetitionController::class);
    Route::post('/competitions/{competition}/participate', [CompetitionController::class, 'participate']);
    Route::get('/competitions/{competition}/participants', [CompetitionController::class, 'getParticipants']);
    
    // Notification management
    Route::apiResource('notifications', NotificationController::class);
    Route::post('/notifications/{notification}/mark-read', [NotificationController::class, 'markAsRead']);
    Route::post('/notifications/mark-all-read', [NotificationController::class, 'markAllAsRead']);
    
    // File attachment management
    Route::apiResource('attachments', AttachmentController::class);
    Route::post('/attachments/upload', [AttachmentController::class, 'upload']);
    
    // Company management (Admin only)
    Route::middleware('role:superAdmin,admin')->group(function () {
        Route::apiResource('companies', CompanyController::class);
        Route::apiResource('discounts', DiscountController::class);
    });
    
    // Admin routes
    Route::middleware('role:superAdmin,admin')->prefix('admin')->name('admin.')->group(function () {
        Route::get('/dashboard', [UserController::class, 'adminDashboard']);
        Route::get('/reports/users', [UserController::class, 'userReports']);
        Route::get('/reports/courses', [CourseController::class, 'courseReports']);
        Route::get('/reports/payments', [PaymentController::class, 'paymentReports']);
        Route::get('/reports/enrollments', [SubscriptionController::class, 'enrollmentReports']);
    });
    
    // Teacher routes
    Route::middleware('role:teacher')->prefix('teacher')->name('teacher.')->group(function () {
        Route::get('/dashboard', [UserController::class, 'teacherDashboard']);
        Route::get('/students', [UserController::class, 'getTeacherStudents']);
        Route::get('/my-groups', [GroupController::class, 'getMyGroups']);
    });
    
    // Student routes
    Route::middleware('role:student')->prefix('student')->name('student.')->group(function () {
        Route::get('/dashboard', [UserController::class, 'studentDashboard']);
        Route::get('/my-courses', [SubscriptionController::class, 'getMyCourses']);
        Route::get('/my-progress', [StudentProgressController::class, 'getMyProgress']);
        Route::get('/my-exams', [StudentExamController::class, 'getMyExams']);
        Route::get('/my-certificates', [CertificateController::class, 'getMyCertificates']);
        Route::get('/my-groups', [GroupController::class, 'getMyGroups']);
    });
});
