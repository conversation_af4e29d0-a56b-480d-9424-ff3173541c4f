<?php

// use App\Http\Controllers\Teacher\DashboardController;
// use App\Http\Controllers\Teacher\CourseController;
// use App\Http\Controllers\Teacher\LessonController;
// use App\Http\Controllers\Teacher\StudentController;
use Illuminate\Support\Facades\Route;

// Teacher routes - protected by auth and role middleware (commented out until controllers are created)
/*
Route::prefix('teacher')->middleware(['auth', 'role:teacher'])->name('teacher.')->group(function () {
    // Teacher dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');

    // Course management
    Route::resource('courses', CourseController::class);
    Route::post('courses/{course}/submit-for-review', [CourseController::class, 'submitForReview'])->name('courses.submit-for-review');

    // Lesson management
    Route::resource('courses.lessons', LessonController::class);

    // Student management
    Route::get('students', [StudentController::class, 'index'])->name('students.index');
    Route::get('students/{user}', [StudentController::class, 'show'])->name('students.show');
    Route::get('students/{user}/progress', [StudentController::class, 'progress'])->name('students.progress');
});
*/