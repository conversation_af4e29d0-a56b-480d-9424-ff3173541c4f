# PowerShell script to set up MySQL database for Soroban Educational Platform
Write-Host "Setting up MySQL database for Soroban Educational Platform..." -ForegroundColor Green
Write-Host ""

Write-Host "Step 1: Creating database and tables..." -ForegroundColor Yellow
try {
    $result = mysql -u root -P 3307 -e "source database/complete_schema.sql"
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Database schema created successfully!" -ForegroundColor Green
        Write-Host ""
        
        Write-Host "Step 2: Checking Laravel migration status..." -ForegroundColor Yellow
        php artisan migrate:status
        Write-Host ""
        
        Write-Host "Step 3: You can now run Laravel migrations if needed..." -ForegroundColor Yellow
        Write-Host "To run fresh migrations, execute: php artisan migrate:fresh" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "Setup completed successfully!" -ForegroundColor Green
    } else {
        throw "MySQL command failed"
    }
} catch {
    Write-Host "Error: Failed to create database schema." -ForegroundColor Red
    Write-Host "Please check your MySQL connection and credentials." -ForegroundColor Red
    Write-Host "Make sure MySQL is running and the credentials in .env are correct." -ForegroundColor Red
}

Read-Host "Press Enter to continue..."
