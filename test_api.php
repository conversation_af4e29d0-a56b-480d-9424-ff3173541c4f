<?php

// Simple API test script
$baseUrl = 'http://localhost:8000/api';

function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        $headers[] = 'Content-Type: application/json';
    }
    
    if (!empty($headers)) {
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'status' => $httpCode,
        'body' => json_decode($response, true)
    ];
}

echo "🚀 Testing Soroban API Endpoints\n";
echo "================================\n\n";

// Test 1: Register a new user
echo "1. Testing User Registration...\n";
$registerData = [
    'first_name' => 'Ahmed',
    'second_name' => 'Ali',
    'email' => '<EMAIL>',
    'password' => 'password123',
    'password_confirmation' => 'password123',
    'age' => 25,
    'role' => 'student',
    'age_group' => 'adults',
    'phone' => '1234567890'
];

$response = makeRequest($baseUrl . '/register', 'POST', $registerData);
echo "Status: " . $response['status'] . "\n";
echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n\n";

$token = null;
if ($response['status'] == 201 && isset($response['body']['data']['token'])) {
    $token = $response['body']['data']['token'];
    echo "✅ Registration successful! Token: " . substr($token, 0, 20) . "...\n\n";
} else {
    echo "❌ Registration failed!\n\n";
}

// Test 2: Login
echo "2. Testing User Login...\n";
$loginData = [
    'email' => '<EMAIL>',
    'password' => 'password123'
];

$response = makeRequest($baseUrl . '/login', 'POST', $loginData);
echo "Status: " . $response['status'] . "\n";
echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n\n";

if ($response['status'] == 200 && isset($response['body']['data']['token'])) {
    $token = $response['body']['data']['token'];
    echo "✅ Login successful!\n\n";
} else {
    echo "❌ Login failed!\n\n";
}

// Test 3: Get user profile (authenticated)
if ($token) {
    echo "3. Testing Get User Profile (Authenticated)...\n";
    $headers = ['Authorization: Bearer ' . $token];
    $response = makeRequest($baseUrl . '/user', 'GET', null, $headers);
    echo "Status: " . $response['status'] . "\n";
    echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n\n";
    
    if ($response['status'] == 200) {
        echo "✅ User profile retrieved successfully!\n\n";
    } else {
        echo "❌ Failed to get user profile!\n\n";
    }
}

// Test 4: Get courses (public)
echo "4. Testing Get Courses (Public)...\n";
$response = makeRequest($baseUrl . '/courses', 'GET');
echo "Status: " . $response['status'] . "\n";
echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n\n";

if ($response['status'] == 200) {
    echo "✅ Courses retrieved successfully!\n\n";
} else {
    echo "❌ Failed to get courses!\n\n";
}

// Test 5: Create a course (authenticated)
if ($token) {
    echo "5. Testing Create Course (Authenticated)...\n";
    $courseData = [
        'name' => 'Soroban Basics',
        'description' => 'Learn the fundamentals of Soroban calculation'
    ];
    
    $headers = ['Authorization: Bearer ' . $token];
    $response = makeRequest($baseUrl . '/courses', 'POST', $courseData, $headers);
    echo "Status: " . $response['status'] . "\n";
    echo "Response: " . json_encode($response['body'], JSON_PRETTY_PRINT) . "\n\n";
    
    if ($response['status'] == 201) {
        echo "✅ Course created successfully!\n\n";
    } else {
        echo "❌ Failed to create course!\n\n";
    }
}

echo "🎉 API Testing Complete!\n";
echo "========================\n";
echo "Your Soroban API is ready for React frontend integration!\n\n";

echo "📋 Available Endpoints:\n";
echo "- POST /api/register - User registration\n";
echo "- POST /api/login - User login\n";
echo "- GET /api/user - Get authenticated user\n";
echo "- GET /api/courses - Get all courses\n";
echo "- POST /api/courses - Create course (auth required)\n";
echo "- GET /api/subscriptions - Get subscriptions\n";
echo "- GET /api/payments - Get payments\n";
echo "- And many more...\n\n";

echo "🔗 Base URL: http://localhost:8000/api\n";
echo "📖 Use Bearer token authentication for protected routes\n";
